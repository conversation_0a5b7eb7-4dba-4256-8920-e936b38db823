#!/usr/bin/env python3
"""
Database connection troubleshooting script.
Tests various aspects of database connectivity.
"""

import os
import sys
import socket
import psycopg2
from urllib.parse import urlparse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_dns_resolution():
    """Test if the database hostname can be resolved"""
    print("🔍 Testing DNS Resolution...")
    
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("   ❌ DATABASE_URL environment variable not found")
        return False
    
    try:
        # Parse the database URL
        parsed = urlparse(database_url)
        hostname = parsed.hostname
        port = parsed.port or 5432
        
        print(f"   🌐 Hostname: {hostname}")
        print(f"   🔌 Port: {port}")
        
        # Test DNS resolution
        try:
            ip_address = socket.gethostbyname(hostname)
            print(f"   ✅ DNS Resolution successful: {ip_address}")
            return True
        except socket.gaierror as e:
            print(f"   ❌ DNS Resolution failed: {e}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error parsing DATABASE_URL: {e}")
        return False

def test_network_connectivity():
    """Test network connectivity to the database server"""
    print("\n🌐 Testing Network Connectivity...")
    
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("   ❌ DATABASE_URL environment variable not found")
        return False
    
    try:
        parsed = urlparse(database_url)
        hostname = parsed.hostname
        port = parsed.port or 5432
        
        # Test TCP connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 10 second timeout
        
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ Network connection successful to {hostname}:{port}")
            return True
        else:
            print(f"   ❌ Network connection failed to {hostname}:{port}")
            return False
            
    except Exception as e:
        print(f"   ❌ Network connectivity test error: {e}")
        return False

def test_database_connection():
    """Test actual database connection"""
    print("\n🗄️  Testing Database Connection...")
    
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("   ❌ DATABASE_URL environment variable not found")
        return False
    
    try:
        # Convert postgres:// to postgresql:// if needed
        if database_url.startswith("postgres://"):
            database_url = database_url.replace("postgres://", "postgresql://", 1)
        
        print(f"   🔗 Attempting connection...")
        
        # Test connection with psycopg2 directly
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        # Test a simple query
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        
        print(f"   ✅ Database connection successful!")
        print(f"   📊 PostgreSQL version: {version[0][:50]}...")
        
        cursor.close()
        conn.close()
        return True
        
    except psycopg2.OperationalError as e:
        print(f"   ❌ Database connection failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def check_environment():
    """Check environment variables and configuration"""
    print("\n⚙️  Checking Environment Configuration...")
    
    database_url = os.getenv("DATABASE_URL")
    if database_url:
        print("   ✅ DATABASE_URL found")
        
        # Parse and display (without sensitive info)
        try:
            parsed = urlparse(database_url)
            print(f"   🌐 Host: {parsed.hostname}")
            print(f"   🔌 Port: {parsed.port or 5432}")
            print(f"   🗄️  Database: {parsed.path[1:] if parsed.path else 'N/A'}")
            print(f"   👤 Username: {parsed.username or 'N/A'}")
            print(f"   🔐 Password: {'***' if parsed.password else 'N/A'}")
        except Exception as e:
            print(f"   ⚠️  Error parsing DATABASE_URL: {e}")
    else:
        print("   ❌ DATABASE_URL not found")
        return False
    
    return True

def suggest_solutions():
    """Suggest potential solutions"""
    print("\n💡 Potential Solutions:")
    print("   1. 🌐 Check your internet connection")
    print("   2. 🔥 Check if your firewall is blocking the connection")
    print("   3. 🏢 If you're on a corporate network, check proxy settings")
    print("   4. 📡 Try using a different DNS server (*******, *******)")
    print("   5. 🔄 Restart your network adapter")
    print("   6. 📞 Contact your database provider (Neon) for status")
    print("   7. 🌍 Try connecting from a different network/location")
    print("   8. ⏰ The database server might be temporarily unavailable")

def main():
    """Main troubleshooting function"""
    print("🚀 Database Connection Troubleshooting")
    print("=" * 50)
    
    # Step 1: Check environment
    if not check_environment():
        print("\n❌ Environment configuration issues found")
        sys.exit(1)
    
    # Step 2: Test DNS resolution
    dns_ok = test_dns_resolution()
    
    # Step 3: Test network connectivity
    network_ok = test_network_connectivity()
    
    # Step 4: Test database connection
    db_ok = test_database_connection()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Troubleshooting Summary:")
    print(f"   DNS Resolution: {'✅ OK' if dns_ok else '❌ FAILED'}")
    print(f"   Network Connectivity: {'✅ OK' if network_ok else '❌ FAILED'}")
    print(f"   Database Connection: {'✅ OK' if db_ok else '❌ FAILED'}")
    
    if not dns_ok:
        print("\n🔍 Primary Issue: DNS Resolution Failed")
        print("   The hostname cannot be resolved to an IP address.")
        print("   This is likely a network or DNS configuration issue.")
    elif not network_ok:
        print("\n🌐 Primary Issue: Network Connectivity Failed")
        print("   DNS works but cannot establish TCP connection.")
        print("   This could be a firewall or network routing issue.")
    elif not db_ok:
        print("\n🗄️  Primary Issue: Database Authentication/Configuration")
        print("   Network connection works but database rejects connection.")
        print("   Check credentials, database name, and permissions.")
    else:
        print("\n🎉 All tests passed! Database connection is working.")
        return
    
    suggest_solutions()

if __name__ == "__main__":
    main()
