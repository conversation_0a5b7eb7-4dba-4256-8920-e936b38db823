"""
Comprehensive test script for the affiliate program
This script tests the complete affiliate system functionality
"""

import requests
import json
from datetime import datetime

# Base URL for your API
BASE_URL = "http://localhost:8000"

def test_affiliate_system():
    """Test the complete affiliate system"""
    print("💰 Testing Complete Affiliate System")
    print("=" * 50)
    
    # Test data
    referrer_data = {
        "username": "<EMAIL>",  # Update with real credentials
        "password": "password123"
    }
    
    referred_data = {
        "username": "<EMAIL>",  # Update with real credentials  
        "password": "password123"
    }
    
    print("1. Testing affiliate settings...")
    settings_response = requests.get(f"{BASE_URL}/affiliate/settings")
    if settings_response.status_code == 200:
        settings = settings_response.json()
        print("✅ Affiliate settings retrieved:")
        print(f"   Commission Type: {settings['commission_type']}")
        print(f"   Fixed Points per Order: {settings['fixed_points_per_order']}")
        print(f"   Program Active: {settings['program_active']}")
        print(f"   Tier 1 Points: {settings['tier1_points']}")
        print(f"   Tier 2 Points: {settings['tier2_points']}")
        print(f"   Tier 3 Points: {settings['tier3_points']}")
    else:
        print("❌ Failed to get affiliate settings")
        return False
    
    print("\n2. Testing user login (referrer)...")
    referrer_login = requests.post(f"{BASE_URL}/auth/login", data=referrer_data)
    if referrer_login.status_code != 200:
        print("❌ Referrer login failed. Please update credentials in script.")
        return False
    
    referrer_token = referrer_login.json()["access_token"]
    referrer_headers = {"Authorization": f"Bearer {referrer_token}"}
    print("✅ Referrer login successful")
    
    print("\n3. Testing affiliate stats...")
    stats_response = requests.get(f"{BASE_URL}/affiliate/stats", headers=referrer_headers)
    if stats_response.status_code == 200:
        stats = stats_response.json()
        print("✅ Affiliate stats retrieved:")
        print(f"   Username: {stats['username']}")
        print(f"   Referral Code: {stats['referral_code']}")
        print(f"   Total Referrals: {stats['total_referrals']}")
        print(f"   Total Commission Earned: {stats['total_commission_earned']}")
        print(f"   Current Tier: {stats['current_tier']}")
    else:
        print("❌ Failed to get affiliate stats")
        return False
    
    print("\n4. Testing commission history...")
    commissions_response = requests.get(f"{BASE_URL}/affiliate/commissions", headers=referrer_headers)
    if commissions_response.status_code == 200:
        commissions = commissions_response.json()
        print(f"✅ Commission history retrieved: {len(commissions)} commissions")
        for commission in commissions[:3]:  # Show first 3
            print(f"   Order #{commission['order_id']}: {commission['commission_points']} points")
    else:
        print("❌ Failed to get commission history")
    
    print("\n5. Testing points balance (should include affiliate commissions)...")
    balance_response = requests.get(f"{BASE_URL}/points/balance", headers=referrer_headers)
    if balance_response.status_code == 200:
        balance = balance_response.json()
        print("✅ Points balance retrieved:")
        print(f"   Points Balance: {balance['points_balance']}")
        print(f"   Total Referrals: {balance['total_referrals']}")
        print(f"   Total Referral Points Earned: {balance['total_referral_points_earned']}")
        print(f"   Referred Users: {len(balance['referred_users'])}")
    else:
        print("❌ Failed to get points balance")
    
    return True

def simulate_order_and_commission():
    """Simulate an order creation that should trigger affiliate commission"""
    print("\n💰 Simulating Order + Affiliate Commission")
    print("=" * 50)
    
    # This would require:
    # 1. A referred user to be logged in
    # 2. The referred user to place an order
    # 3. The system to automatically award commission to the referrer
    
    print("To test affiliate commissions:")
    print("1. Register a new user with a referral code")
    print("2. Have that user place an order (any amount)")
    print("3. Check that the referrer receives commission points")
    print("4. Verify the commission email is sent")
    print("5. Check the affiliate stats and commission history")

def test_admin_endpoints():
    """Test admin endpoints (requires admin authentication)"""
    print("\n🔧 Testing Admin Endpoints")
    print("=" * 50)
    
    # Note: These would require proper admin authentication
    print("Admin endpoints available:")
    print("• POST /affiliate/admin/settings - Update affiliate settings")
    print("• GET /affiliate/admin/users - View all affiliate users")
    print("• POST /affiliate/admin/user/{user_id}/commission - Award manual commission")
    print("\nNote: These require admin authentication to test")

def display_api_documentation():
    """Display API documentation for the affiliate system"""
    print("\n📚 Affiliate System API Documentation")
    print("=" * 50)
    
    endpoints = [
        {
            "method": "GET",
            "endpoint": "/affiliate/stats",
            "description": "Get current user's affiliate statistics",
            "auth": "Required"
        },
        {
            "method": "GET", 
            "endpoint": "/affiliate/commissions",
            "description": "Get current user's commission history",
            "auth": "Required"
        },
        {
            "method": "GET",
            "endpoint": "/affiliate/settings", 
            "description": "Get affiliate program settings",
            "auth": "None"
        },
        {
            "method": "POST",
            "endpoint": "/affiliate/admin/settings",
            "description": "Update affiliate program settings",
            "auth": "Admin"
        },
        {
            "method": "GET",
            "endpoint": "/affiliate/admin/users",
            "description": "Get all affiliate users",
            "auth": "Admin"
        }
    ]
    
    for endpoint in endpoints:
        print(f"{endpoint['method']} {endpoint['endpoint']}")
        print(f"   Description: {endpoint['description']}")
        print(f"   Auth: {endpoint['auth']}")
        print()

def main():
    """Main test function"""
    print("💰 Affiliate System Test Suite")
    print("=" * 50)
    
    print("⚠️  IMPORTANT: Update the login credentials in this script before running!")
    print("   Edit the 'referrer_data' and 'referred_data' dictionaries")
    print()
    
    # Display API documentation
    display_api_documentation()
    
    print("\n🔗 Email Template Previews:")
    print("=" * 50)
    print("• Affiliate Commission: http://localhost:8000/email-preview/affiliate-commission")
    print("• Tier Upgrade: http://localhost:8000/email-preview/affiliate-tier-upgrade")
    
    print("\n🧪 Test Flow:")
    print("=" * 50)
    print("1. Initialize affiliate settings: python create_affiliate_settings.py")
    print("2. Register users with referral codes")
    print("3. Have referred users place orders")
    print("4. Check affiliate commissions are awarded")
    print("5. Verify emails are sent")
    
    print("\n📊 How Commissions Work:")
    print("=" * 50)
    print("• Bronze Tier (0+ orders): 25 points per order")
    print("• Silver Tier (10+ orders): 35 points per order")
    print("• Gold Tier (25+ orders): 50 points per order")
    print("• Commissions are awarded for ANY order placed by referred users")
    print("• Points are automatically added to referrer's balance")
    print("• Email notifications are sent for each commission")
    
    # Uncomment to run actual tests (after updating credentials)
    # test_affiliate_system()
    # simulate_order_and_commission()
    # test_admin_endpoints()

if __name__ == "__main__":
    main()
