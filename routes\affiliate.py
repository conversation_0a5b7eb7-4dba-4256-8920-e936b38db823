from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from typing import List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

from models.user import User
from models.affiliate import (
    AffiliateSettings, AffiliateUser, AffiliateCommission, AffiliatePayment,
    AffiliateCommissionType, AffiliateStatus
)
from models.points_transaction import PointsTransaction, TransactionType
from auth import get_current_user
from db import get_session
from utils.referral import add_points_to_user

affiliate_router = APIRouter(prefix="/affiliate", tags=["Affiliate Program"])

# Response models
class AffiliateStatsResponse(BaseModel):
    user_id: int
    username: str
    referral_code: Optional[str] = None  # Only shown if user has access
    affiliate_status: str
    total_referrals: int
    total_orders_from_referrals: int
    total_commission_earned: int
    current_tier: int
    monthly_commission: int
    joined_at: datetime

class CommissionResponse(BaseModel):
    id: int
    commission_points: int
    order_id: int
    referred_user_id: int
    order_value: float
    description: str
    created_at: datetime
    status: str

class AffiliateSettingsResponse(BaseModel):
    commission_type: str
    fixed_points_per_order: int
    percentage_rate: float
    min_order_value: float
    program_active: bool
    tier1_points: int
    tier2_points: int
    tier3_points: int

# Request models
class UpdateAffiliateSettingsRequest(BaseModel):
    commission_type: Optional[str] = None
    fixed_points_per_order: Optional[int] = None
    percentage_rate: Optional[float] = None
    min_order_value: Optional[float] = None
    program_active: Optional[bool] = None
    tier1_points: Optional[int] = None
    tier2_points: Optional[int] = None
    tier3_points: Optional[int] = None

# Get user's affiliate stats
@affiliate_router.get("/stats", response_model=AffiliateStatsResponse)
def get_affiliate_stats(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """Get current user's affiliate statistics"""

    # Check if user has referral code access
    if not current_user.referral_code_access_enabled:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Referral code access not enabled. Please complete your first order and wait for admin approval."
        )
    
    # Get or create affiliate user record
    affiliate_user = session.exec(
        select(AffiliateUser).where(AffiliateUser.user_id == current_user.id)
    ).first()
    
    if not affiliate_user:
        # Create new affiliate user record
        affiliate_user = AffiliateUser(
            user_id=current_user.id,
            total_referrals=len(session.exec(select(User).where(User.referred_by == current_user.id)).all())
        )
        session.add(affiliate_user)
        session.commit()
        session.refresh(affiliate_user)
    
    return AffiliateStatsResponse(
        user_id=current_user.id,
        username=current_user.username,
        referral_code=current_user.referral_code,
        affiliate_status=affiliate_user.status,
        total_referrals=affiliate_user.total_referrals,
        total_orders_from_referrals=affiliate_user.total_orders_from_referrals,
        total_commission_earned=affiliate_user.total_commission_earned,
        current_tier=affiliate_user.current_tier,
        monthly_commission=affiliate_user.monthly_commission,
        joined_at=affiliate_user.joined_at
    )

# Get user's commission history
@affiliate_router.get("/commissions", response_model=List[CommissionResponse])
def get_commission_history(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
    limit: int = 50,
    offset: int = 0
):
    """Get current user's commission history"""
    
    # Get affiliate user record
    affiliate_user = session.exec(
        select(AffiliateUser).where(AffiliateUser.user_id == current_user.id)
    ).first()
    
    if not affiliate_user:
        return []
    
    # Get commissions
    commissions = session.exec(
        select(AffiliateCommission)
        .where(AffiliateCommission.affiliate_user_id == affiliate_user.id)
        .order_by(AffiliateCommission.created_at.desc())
        .offset(offset)
        .limit(limit)
    ).all()
    
    return [
        CommissionResponse(
            id=c.id,
            commission_points=c.commission_points,
            order_id=c.order_id,
            referred_user_id=c.referred_user_id,
            order_value=c.order_value,
            description=c.description,
            created_at=c.created_at,
            status=c.status
        )
        for c in commissions
    ]

# Get affiliate program settings
@affiliate_router.get("/settings", response_model=AffiliateSettingsResponse)
def get_affiliate_settings(session: Session = Depends(get_session)):
    """Get current affiliate program settings"""
    
    settings = session.exec(select(AffiliateSettings)).first()
    if not settings:
        # Create default settings
        settings = AffiliateSettings()
        session.add(settings)
        session.commit()
        session.refresh(settings)
    
    return AffiliateSettingsResponse(
        commission_type=settings.commission_type,
        fixed_points_per_order=settings.fixed_points_per_order,
        percentage_rate=settings.percentage_rate,
        min_order_value=settings.min_order_value,
        program_active=settings.program_active,
        tier1_points=settings.tier1_points,
        tier2_points=settings.tier2_points,
        tier3_points=settings.tier3_points
    )

# Function to process affiliate commission (called from order system)
def process_affiliate_commission(referred_user: User, order_id: int, order_value: float, session: Session):
    """Process affiliate commission when a referred user places an order"""
    
    print(f"💰 AFFILIATE DEBUG: Processing commission for order {order_id}")
    print(f"   Referred User: {referred_user.username} (ID: {referred_user.id})")
    print(f"   Order Value: ${order_value}")
    print(f"   Referred By: {referred_user.referred_by}")
    
    # Check if user was referred by someone
    if not referred_user.referred_by:
        print("   ❌ User was not referred by anyone")
        return
    
    # Get the referring user
    referring_user = session.get(User, referred_user.referred_by)
    if not referring_user:
        print("   ❌ Referring user not found")
        return
    
    print(f"   Referring User: {referring_user.username} (ID: {referring_user.id})")
    
    # Get affiliate settings
    settings = session.exec(select(AffiliateSettings)).first()
    if not settings:
        settings = AffiliateSettings()
        session.add(settings)
        session.commit()
        session.refresh(settings)
    
    if not settings.program_active:
        print("   ❌ Affiliate program is not active")
        return
    
    # Check minimum order value
    if order_value < settings.min_order_value:
        print(f"   ❌ Order value ${order_value} below minimum ${settings.min_order_value}")
        return
    
    # Get or create affiliate user record
    affiliate_user = session.exec(
        select(AffiliateUser).where(AffiliateUser.user_id == referring_user.id)
    ).first()
    
    if not affiliate_user:
        affiliate_user = AffiliateUser(
            user_id=referring_user.id,
            total_referrals=len(session.exec(select(User).where(User.referred_by == referring_user.id)).all())
        )
        session.add(affiliate_user)
        session.commit()
        session.refresh(affiliate_user)
    
    # Check if affiliate is active
    if affiliate_user.status != AffiliateStatus.ACTIVE:
        print(f"   ❌ Affiliate status is {affiliate_user.status}")
        return
    
    # Calculate commission points
    commission_points = calculate_commission_points(affiliate_user, settings, order_value)
    
    print(f"   💰 Commission Points: {commission_points}")
    
    if commission_points <= 0:
        print("   ❌ No commission points calculated")
        return
    
    # Create commission record
    commission = AffiliateCommission(
        affiliate_user_id=affiliate_user.id,
        referrer_user_id=referring_user.id,
        referred_user_id=referred_user.id,
        order_id=order_id,
        commission_points=commission_points,
        commission_type=settings.commission_type,
        order_value=order_value,
        description=f"Commission for order #{order_id} by {referred_user.username}",
        tier_level=affiliate_user.current_tier,
        status="paid"  # Automatically mark as paid since we're adding points immediately
    )
    
    session.add(commission)
    
    # Update affiliate user stats
    affiliate_user.total_orders_from_referrals += 1
    affiliate_user.total_commission_earned += commission_points
    affiliate_user.monthly_commission += commission_points
    affiliate_user.last_commission_date = datetime.utcnow()
    
    # Update tier if needed
    update_affiliate_tier(affiliate_user, settings)
    
    session.add(affiliate_user)
    
    # Add points to referring user
    try:
        add_points_to_user(
            user=referring_user,
            points=commission_points,
            transaction_type=TransactionType.TASK_COMPLETION,  # Using task completion for affiliate commissions
            description=f"Affiliate commission: Order #{order_id} by {referred_user.username}",
            session=session,
            reference_id=f"affiliate_order_{order_id}"
        )
        
        session.commit()
        print(f"   ✅ Successfully awarded {commission_points} points to {referring_user.username}")
        
        # Send affiliate commission email
        try:
            send_affiliate_commission_email(referring_user, referred_user, commission_points, order_id, order_value)
        except Exception as e:
            print(f"   ⚠️ Error sending affiliate email: {str(e)}")
        
    except Exception as e:
        session.rollback()
        print(f"   ❌ Error processing affiliate commission: {str(e)}")

def calculate_commission_points(affiliate_user: AffiliateUser, settings: AffiliateSettings, order_value: float) -> int:
    """Calculate commission points based on settings and user tier"""
    
    if settings.commission_type == AffiliateCommissionType.FIXED_POINTS:
        # Use tier-based points if tiered system
        if settings.commission_type == AffiliateCommissionType.TIERED:
            if affiliate_user.current_tier == 1:
                return settings.tier1_points
            elif affiliate_user.current_tier == 2:
                return settings.tier2_points
            elif affiliate_user.current_tier >= 3:
                return settings.tier3_points
        
        # Use custom rate if set, otherwise use global rate
        return affiliate_user.custom_commission_rate or settings.fixed_points_per_order
    
    elif settings.commission_type == AffiliateCommissionType.PERCENTAGE:
        rate = affiliate_user.custom_percentage_rate or settings.percentage_rate
        return int((order_value * rate) / 100)
    
    return settings.fixed_points_per_order

def update_affiliate_tier(affiliate_user: AffiliateUser, settings: AffiliateSettings):
    """Update affiliate user's tier based on their performance"""
    
    total_orders = affiliate_user.total_orders_from_referrals
    
    if total_orders >= settings.tier3_orders:
        affiliate_user.current_tier = 3
    elif total_orders >= settings.tier2_orders:
        affiliate_user.current_tier = 2
    else:
        affiliate_user.current_tier = 1

def send_affiliate_commission_email(referring_user: User, referred_user: User, commission_points: int, order_id: int, order_value: float = 0.0):
    """Send affiliate commission notification email"""
    from templates.email_templates import create_affiliate_commission_email
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart

    def send_email(to_email, subject, body):
        from_email = "<EMAIL>"
        from_password = "Fundedwhales@9"

        msg = MIMEMultipart()
        msg["From"] = from_email
        msg["To"] = to_email
        msg["Subject"] = subject
        msg.attach(MIMEText(body, "html"))

        with smtplib.SMTP_SSL("smtp.hostinger.com", 465) as server:
            server.login(from_email, from_password)
            server.sendmail(from_email, to_email, msg.as_string())

    subject = f"💰 You Earned {commission_points} Points! - Affiliate Commission"
    html_content = create_affiliate_commission_email(
        referring_user.username,
        referred_user.username,
        commission_points,
        order_id,
        order_value,
        current_tier=1  # You can get this from affiliate_user.current_tier
    )

    send_email(referring_user.email, subject, html_content)
    print(f"📧 Sent affiliate commission email to {referring_user.email}")
    print(f"   Commission: {commission_points} points for order #{order_id}")

# Admin endpoints
@affiliate_router.post("/admin/settings")
def update_affiliate_settings(
    request: UpdateAffiliateSettingsRequest,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Update affiliate program settings (accessible by any logged-in user)"""

    settings = session.exec(select(AffiliateSettings)).first()
    if not settings:
        settings = AffiliateSettings()

    # Update fields if provided
    if request.commission_type is not None:
        settings.commission_type = AffiliateCommissionType(request.commission_type)
    if request.fixed_points_per_order is not None:
        settings.fixed_points_per_order = request.fixed_points_per_order
    if request.percentage_rate is not None:
        settings.percentage_rate = request.percentage_rate
    if request.min_order_value is not None:
        settings.min_order_value = request.min_order_value
    if request.program_active is not None:
        settings.program_active = request.program_active
    if request.tier1_points is not None:
        settings.tier1_points = request.tier1_points
    if request.tier2_points is not None:
        settings.tier2_points = request.tier2_points
    if request.tier3_points is not None:
        settings.tier3_points = request.tier3_points

    settings.updated_at = datetime.utcnow()

    session.add(settings)
    session.commit()
    session.refresh(settings)

    return {
        "message": "Affiliate settings updated successfully",
        "settings": {
            "commission_type": settings.commission_type,
            "fixed_points_per_order": settings.fixed_points_per_order,
            "percentage_rate": settings.percentage_rate,
            "min_order_value": settings.min_order_value,
            "program_active": settings.program_active
        }
    }

@affiliate_router.get("/admin/users", response_model=List[AffiliateStatsResponse])
def get_all_affiliate_users(
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user),
    limit: int = 100,
    offset: int = 0
):
    """Get all affiliate users (accessible by any logged-in user)"""

    affiliate_users = session.exec(
        select(AffiliateUser, User)
        .join(User, AffiliateUser.user_id == User.id)
        .order_by(AffiliateUser.total_commission_earned.desc())
        .offset(offset)
        .limit(limit)
    ).all()

    return [
        AffiliateStatsResponse(
            user_id=user.id,
            username=user.username,
            referral_code=user.referral_code if user.referral_code_access_enabled else None,
            affiliate_status=affiliate_user.status,
            total_referrals=affiliate_user.total_referrals,
            total_orders_from_referrals=affiliate_user.total_orders_from_referrals,
            total_commission_earned=affiliate_user.total_commission_earned,
            current_tier=affiliate_user.current_tier,
            monthly_commission=affiliate_user.monthly_commission,
            joined_at=affiliate_user.joined_at
        )
        for affiliate_user, user in affiliate_users
    ]

@affiliate_router.post("/admin/user/{user_id}/commission")
def award_manual_commission(
    user_id: int,
    points: int,
    description: str,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Manually award commission points to a user (accessible by any logged-in user)"""

    target_user = session.get(User, user_id)
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Get or create affiliate user record
    affiliate_user = session.exec(
        select(AffiliateUser).where(AffiliateUser.user_id == user_id)
    ).first()

    if not affiliate_user:
        affiliate_user = AffiliateUser(user_id=user_id)
        session.add(affiliate_user)
        session.commit()
        session.refresh(affiliate_user)

    # Add points to user
    add_points_to_user(
        user=target_user,
        points=points,
        transaction_type=TransactionType.ADMIN_ADJUSTMENT,
        description=f"Manual affiliate commission: {description}",
        session=session,
        reference_id=f"manual_commission_{datetime.utcnow().timestamp()}"
    )

    # Update affiliate stats
    affiliate_user.total_commission_earned += points
    affiliate_user.monthly_commission += points
    session.add(affiliate_user)

    session.commit()

    return {
        "message": f"Awarded {points} commission points to {target_user.username}",
        "user": target_user.username,
        "points_awarded": points,
        "new_total_commission": affiliate_user.total_commission_earned
    }
