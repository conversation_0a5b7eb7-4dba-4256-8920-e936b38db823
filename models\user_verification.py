from sqlmodel import SQLModel, <PERSON>
from datetime import datetime
from typing import Optional
from .user import User

class UserVerification(SQLModel, table=True):
    __tablename__ = "user_verifications"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id", unique=True)  # One-to-one relationship with User
    is_verified: bool = Field(default=False)
    verified_at: Optional[datetime] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow) 