#!/usr/bin/env python3
"""
Fix SSL connection issues with Neon database on Heroku.
This script helps update the DATABASE_URL with proper SSL parameters.
"""

import os
import re
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

def fix_database_url():
    """Fix DATABASE_URL for SSL connection issues"""
    print("🔧 Fixing DATABASE_URL for SSL connection issues...")
    
    # Read current .env file
    env_file = ".env"
    if not os.path.exists(env_file):
        print("❌ .env file not found")
        return False
    
    with open(env_file, 'r') as f:
        content = f.read()
    
    # Find DATABASE_URL line
    database_url_match = re.search(r'DATABASE_URL\s*=\s*(.+)', content)
    if not database_url_match:
        print("❌ DATABASE_URL not found in .env file")
        return False
    
    original_url = database_url_match.group(1).strip().strip('"\'')
    print(f"📋 Original URL: {original_url[:50]}...")
    
    # Parse the URL
    parsed = urlparse(original_url)
    
    # Get existing query parameters
    query_params = parse_qs(parsed.query)
    
    # Add/update SSL parameters for Neon database
    ssl_params = {
        'sslmode': ['require'],
        'sslcert': [''],
        'sslkey': [''],
        'sslrootcert': ['']
    }
    
    # Update query parameters
    query_params.update(ssl_params)
    
    # Rebuild the URL
    new_query = urlencode(query_params, doseq=True)
    new_parsed = parsed._replace(query=new_query)
    fixed_url = urlunparse(new_parsed)
    
    print(f"🔧 Fixed URL: {fixed_url[:50]}...")
    
    # Update the .env file
    new_content = re.sub(
        r'DATABASE_URL\s*=\s*.+',
        f'DATABASE_URL={fixed_url}',
        content
    )
    
    # Backup original .env
    with open(f"{env_file}.backup", 'w') as f:
        f.write(content)
    print("💾 Created backup: .env.backup")
    
    # Write updated .env
    with open(env_file, 'w') as f:
        f.write(new_content)
    
    print("✅ Updated .env file with SSL fixes")
    return True

def create_heroku_ssl_config():
    """Create Heroku-specific SSL configuration"""
    print("\n🚀 Creating Heroku SSL configuration...")
    
    heroku_config = '''
# Heroku SSL Configuration for Neon Database
# Add these to your Heroku config vars:

# Method 1: Update DATABASE_URL directly in Heroku
# heroku config:set DATABASE_URL="your_neon_url?sslmode=require&sslcert=&sslkey=&sslrootcert="

# Method 2: Add SSL-specific config vars
# heroku config:set PGSSLMODE=require
# heroku config:set PGSSLCERT=""
# heroku config:set PGSSLKEY=""
# heroku config:set PGSSLROOTCERT=""

# Method 3: Disable SSL verification (NOT recommended for production)
# heroku config:set PGSSLMODE=prefer
'''
    
    with open("heroku_ssl_config.txt", "w") as f:
        f.write(heroku_config)
    
    print("✅ Created heroku_ssl_config.txt")

def test_ssl_connection():
    """Test the SSL connection with the fixed URL"""
    print("\n🧪 Testing SSL connection...")
    
    try:
        import psycopg2
        from dotenv import load_dotenv
        
        # Reload environment variables
        load_dotenv(override=True)
        
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            print("❌ DATABASE_URL not found")
            return False
        
        # Convert postgres:// to postgresql://
        if database_url.startswith("postgres://"):
            database_url = database_url.replace("postgres://", "postgresql://", 1)
        
        print("🔗 Attempting connection with SSL fixes...")
        
        # Test connection
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        
        print(f"✅ SSL connection successful!")
        print(f"📊 PostgreSQL version: {version[0][:50]}...")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ SSL connection test failed: {e}")
        
        if "SSL connection has been closed unexpectedly" in str(e):
            print("🔐 Still having SSL issues. Try these additional fixes:")
            print("   1. Use sslmode=prefer instead of require")
            print("   2. Contact Neon support about SSL certificate issues")
            print("   3. Try connecting from a different network")
        
        return False

def provide_heroku_deployment_tips():
    """Provide tips for Heroku deployment"""
    print("\n🚀 Heroku Deployment Tips for SSL Issues:")
    print("=" * 50)
    
    print("1. 🔧 Update Heroku Config Vars:")
    print("   heroku config:set DATABASE_URL='your_fixed_url'")
    
    print("\n2. 🔐 SSL Mode Options (try in order):")
    print("   - sslmode=require (most secure)")
    print("   - sslmode=prefer (fallback)")
    print("   - sslmode=allow (last resort)")
    
    print("\n3. 📦 Ensure Dependencies:")
    print("   - psycopg2-binary in requirements.txt")
    print("   - sqlalchemy with proper version")
    
    print("\n4. 🔄 Restart Heroku App:")
    print("   heroku restart")
    
    print("\n5. 📊 Check Heroku Logs:")
    print("   heroku logs --tail")
    
    print("\n6. 🌐 Alternative: Use Heroku Postgres:")
    print("   heroku addons:create heroku-postgresql:mini")
    print("   (This might be more reliable than external Neon)")

def main():
    """Main function"""
    print("🚀 SSL Connection Fix for Neon Database")
    print("=" * 50)
    
    # Fix DATABASE_URL
    if fix_database_url():
        print("✅ DATABASE_URL updated with SSL fixes")
    else:
        print("❌ Failed to update DATABASE_URL")
        return
    
    # Create Heroku config
    create_heroku_ssl_config()
    
    # Test connection
    test_ssl_connection()
    
    # Provide deployment tips
    provide_heroku_deployment_tips()
    
    print("\n" + "=" * 50)
    print("📋 Summary:")
    print("   ✅ Updated .env file with SSL parameters")
    print("   ✅ Created Heroku configuration guide")
    print("   ✅ Tested SSL connection")
    
    print("\n🔧 Next Steps:")
    print("   1. Test your app locally: python -m uvicorn hello:app --reload")
    print("   2. Deploy to Heroku: git push heroku main")
    print("   3. Check Heroku logs: heroku logs --tail")
    print("   4. If issues persist, try sslmode=prefer")

if __name__ == "__main__":
    main()
