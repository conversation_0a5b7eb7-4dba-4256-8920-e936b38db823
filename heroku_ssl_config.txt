
# Heroku SSL Configuration for Neon Database
# Add these to your Heroku config vars:

# Method 1: Update DATABASE_URL directly in Heroku
# heroku config:set DATABASE_URL="your_neon_url?sslmode=require&sslcert=&sslkey=&sslrootcert="

# Method 2: Add SSL-specific config vars
# heroku config:set PGSSLMODE=require
# heroku config:set PGSSLCERT=""
# heroku config:set PGSSLKEY=""
# heroku config:set PGSSLROOTCERT=""

# Method 3: Disable SSL verification (NOT recommended for production)
# heroku config:set PGSSLMODE=prefer
