from sqlmodel import SQLModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum

class GiveawayStatus(str, Enum):
    ACTIVE = "active"
    ENDED = "ended"
    CANCELLED = "cancelled"

class GiveawayEntryStatus(str, Enum):
    ACTIVE = "active"
    WINNER = "winner"
    DISQUALIFIED = "disqualified"

class Giveaway(SQLModel, table=True):
    __tablename__ = "giveaways"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    title: str = Field(max_length=200)
    description: str
    prize_description: str  # e.g., "$100,000 Funded Trading Account"
    prize_value: float  # Prize value in USD
    
    # Eligibility criteria
    min_account_size: float = Field(default=50000.0)  # Minimum account size to qualify
    
    # Dates
    start_date: datetime
    end_date: datetime
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Status
    status: GiveawayStatus = Field(default=GiveawayStatus.ACTIVE)
    
    # Winner information
    winner_id: Optional[int] = Field(default=None)  # Remove foreign key for now
    winner_selected_at: Optional[datetime] = Field(default=None)
    
    # Statistics
    total_entries: int = Field(default=0)
    max_entries: Optional[int] = Field(default=None)  # Maximum number of entries allowed
    
    # Terms and conditions
    terms_and_conditions: Optional[str] = Field(default=None)

class GiveawayEntry(SQLModel, table=True):
    __tablename__ = "giveaway_entries"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    giveaway_id: int = Field(foreign_key="giveaways.id", index=True)
    user_id: int = Field(index=True)  # Remove foreign key for now
    order_id: int = Field(index=True)  # Remove foreign key for now
    
    # Entry details
    account_size: float  # Size of the account that qualified them
    entry_date: datetime = Field(default_factory=datetime.utcnow)
    
    # Status
    status: GiveawayEntryStatus = Field(default=GiveawayEntryStatus.ACTIVE)
    
    # Additional information
    notes: Optional[str] = Field(default=None)
    
    # Unique constraint to prevent duplicate entries per user per giveaway
    __table_args__ = {"extend_existing": True}

class GiveawayWinner(SQLModel, table=True):
    __tablename__ = "giveaway_winners"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    giveaway_id: int = Field(foreign_key="giveaways.id")
    user_id: int  # Remove foreign key for now
    entry_id: int = Field(foreign_key="giveaway_entries.id")
    
    # Winner details
    selected_at: datetime = Field(default_factory=datetime.utcnow)
    notified_at: Optional[datetime] = Field(default=None)
    prize_claimed: bool = Field(default=False)
    prize_claimed_at: Optional[datetime] = Field(default=None)
    
    # Prize delivery information
    account_credentials_sent: bool = Field(default=False)
    account_server: Optional[str] = Field(default=None)
    account_login: Optional[str] = Field(default=None)
    account_password: Optional[str] = Field(default=None)
    
    # Notes
    notes: Optional[str] = Field(default=None)
