from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session, select
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

from models.user import User
from models.points_transaction import PointsTransaction, TransactionType
from auth import get_current_user
from db import get_session
from utils.referral import add_points_to_user, deduct_points_from_user

points_router = APIRouter(prefix="/points", tags=["Points"])

# Response models
class ReferredUserInfo(BaseModel):
    id: int
    username: str
    email: str
    created_at: datetime

class PointsBalanceResponse(BaseModel):
    user_id: int
    username: str
    email: str
    points_balance: int
    referral_code: str
    total_referrals: int
    total_referral_points_earned: int
    referred_users: List[ReferredUserInfo]

class PointsTransactionResponse(BaseModel):
    id: int
    transaction_type: str
    points_amount: int
    description: str
    reference_id: Optional[str]
    created_at: datetime
    referred_user_id: Optional[int]

class AddPointsRequest(BaseModel):
    points: int
    description: str
    reference_id: Optional[str] = None

class DeductPointsRequest(BaseModel):
    points: int
    description: str
    reference_id: Optional[str] = None

# Get current user's points balance
@points_router.get("/balance", response_model=PointsBalanceResponse)
def get_points_balance(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """Get the current user's points balance, referral code, and referred users"""

    # Get users who registered with current user's referral code
    referred_users = session.exec(
        select(User).where(User.referred_by == current_user.id)
    ).all()

    # Get referral bonus transactions to calculate total referral points earned
    referral_transactions = session.exec(
        select(PointsTransaction)
        .where(PointsTransaction.user_id == current_user.id)
        .where(PointsTransaction.transaction_type == TransactionType.REFERRAL_BONUS)
    ).all()

    total_referral_points = sum(t.points_amount for t in referral_transactions)

    # Convert referred users to response format
    referred_users_info = [
        ReferredUserInfo(
            id=user.id,
            username=user.username,
            email=user.email,
            created_at=user.created_at
        )
        for user in referred_users
    ]

    return PointsBalanceResponse(
        user_id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        points_balance=current_user.points_balance,
        referral_code=current_user.referral_code,
        total_referrals=len(referred_users),
        total_referral_points_earned=total_referral_points,
        referred_users=referred_users_info
    )

# Get user's transaction history
@points_router.get("/transactions", response_model=List[PointsTransactionResponse])
def get_transaction_history(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
    limit: int = Query(default=50, le=100),
    offset: int = Query(default=0, ge=0)
):
    """Get the current user's points transaction history"""
    transactions = session.exec(
        select(PointsTransaction)
        .where(PointsTransaction.user_id == current_user.id)
        .order_by(PointsTransaction.created_at.desc())
        .offset(offset)
        .limit(limit)
    ).all()
    
    return [
        PointsTransactionResponse(
            id=t.id,
            transaction_type=t.transaction_type,
            points_amount=t.points_amount,
            description=t.description,
            reference_id=t.reference_id,
            created_at=t.created_at,
            referred_user_id=t.referred_user_id
        )
        for t in transactions
    ]

# Get referral statistics
@points_router.get("/referrals")
def get_referral_stats(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """Get referral statistics for the current user"""
    # Count users referred by current user
    referred_users_count = session.exec(
        select(User).where(User.referred_by == current_user.id)
    ).all()
    
    # Count referral bonus transactions
    referral_transactions = session.exec(
        select(PointsTransaction)
        .where(PointsTransaction.user_id == current_user.id)
        .where(PointsTransaction.transaction_type == TransactionType.REFERRAL_BONUS)
    ).all()
    
    total_referral_points = sum(t.points_amount for t in referral_transactions)
    
    return {
        "referral_code": current_user.referral_code,
        "total_referrals": len(referred_users_count),
        "total_referral_points_earned": total_referral_points,
        "referred_users": [
            {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "created_at": user.created_at
            }
            for user in referred_users_count
        ]
    }

# Admin endpoint to add points to a user
@points_router.post("/admin/add")
def admin_add_points(
    user_id: int,
    request: AddPointsRequest,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Admin endpoint to add points to any user (accessible by any logged-in user)"""
    
    target_user = session.get(User, user_id)
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    transaction = add_points_to_user(
        user=target_user,
        points=request.points,
        transaction_type=TransactionType.ADMIN_ADJUSTMENT,
        description=f"Admin adjustment: {request.description}",
        session=session,
        reference_id=request.reference_id
    )
    
    session.commit()
    
    return {
        "message": f"Added {request.points} points to user {target_user.username}",
        "new_balance": target_user.points_balance,
        "transaction_id": transaction.id
    }

# Admin endpoint to deduct points from a user
@points_router.post("/admin/deduct")
def admin_deduct_points(
    user_id: int,
    request: DeductPointsRequest,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Admin endpoint to deduct points from any user (accessible by any logged-in user)"""
    
    target_user = session.get(User, user_id)
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    transaction = deduct_points_from_user(
        user=target_user,
        points=request.points,
        transaction_type=TransactionType.ADMIN_ADJUSTMENT,
        description=f"Admin deduction: {request.description}",
        session=session,
        reference_id=request.reference_id
    )
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient points balance"
        )
    
    session.commit()
    
    return {
        "message": f"Deducted {request.points} points from user {target_user.username}",
        "new_balance": target_user.points_balance,
        "transaction_id": transaction.id
    }

# Endpoint to award task completion points
@points_router.post("/task-completion")
def award_task_points(
    task_name: str,
    points: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Award points for completing a task"""
    transaction = add_points_to_user(
        user=current_user,
        points=points,
        transaction_type=TransactionType.TASK_COMPLETION,
        description=f"Task completed: {task_name}",
        session=session,
        reference_id=task_name
    )
    
    session.commit()
    
    return {
        "message": f"Awarded {points} points for completing task: {task_name}",
        "new_balance": current_user.points_balance,
        "transaction_id": transaction.id
    }
