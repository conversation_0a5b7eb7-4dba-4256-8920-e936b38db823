from sqlmodel import SQLModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum

class TransactionType(str, Enum):
    REFERRAL_BONUS = "referral_bonus"
    TASK_COMPLETION = "task_completion"
    PURCHASE = "purchase"
    ADMIN_ADJUSTMENT = "admin_adjustment"
    WITHDRAWAL = "withdrawal"

class PointsTransaction(SQLModel, table=True):
    __tablename__ = "points_transactions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id", index=True)
    transaction_type: TransactionType
    points_amount: int  # Positive for earning, negative for spending
    description: str
    reference_id: Optional[str] = Field(default=None)  # For referencing orders, tasks, etc.
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Optional fields for referral tracking
    referred_user_id: Optional[int] = Field(default=None, foreign_key="user.id")
