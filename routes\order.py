import os
from fastapi import Depends, FastAPI, Form, APIRouter, HTTPException
from sqlmodel import SQLModel, create_engine, Session, Field, select, Relationship
from pydantic import BaseModel
from typing import Optional, List
import smtplib
from db import get_session
from models.user import User
from models.order import (
    OrderModel, OrderImage, DateTimeModel, PassOrder, OrderTimeline,
    CompleteOrderModel, Stage2Account, LiveAccount, OrderStatus,
    RejectOrder, FailOrder, Certificate
)
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from auth import get_current_user
from datetime import datetime, timedelta
import random
from enum import Enum
import cloudinary
import cloudinary.uploader
import time
from templates.email_templates import (
    create_order_confirmation_email,
    create_challenge_completion_email,
    create_pass_notification_email,
    create_fail_notification_email,
    create_live_account_email,
    create_certificate_email
)

# Configure Cloudinary
cloudinary.config(
    cloud_name=os.getenv("CLOUDINARY_CLOUD_NAME"),
    api_key=os.getenv("CLOUDINARY_API_KEY"),
    api_secret=os.getenv("CLOUDINARY_API_SECRET")
)

order_router = APIRouter(prefix="/order")
app = FastAPI()

class Order(BaseModel):
    username: str
    challenge_type: str
    account_size: str
    platform: str
    payment_method: str
    txid: str

class AccountCredentials(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    server: str
    platform_login: str
    platform_password: str
    is_assigned: bool = False

DATABASE_URL = os.environ.get("DATABASE_URL", "").replace(
    "postgres://", "postgresql+psycopg2://", 1
)
engine = create_engine(DATABASE_URL, echo=True)

SQLModel.metadata.create_all(engine)

def create_html_email_template(title, content, button_text=None, button_url=None):
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {{
                font-family: 'Segoe UI', Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 0;
                background-color: #0a1a0a;
                color: #e6e6e6;
            }}
            .container {{
                max-width: 600px;
                margin: 20px auto;
                padding: 20px;
                background: linear-gradient(145deg, #0a2a0a 0%, #0d3d0d 100%);
                border-radius: 5px;
                border: 1px solid #1e5f1e;
                box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            }}
            .header {{
                text-align: center;
                padding: 20px 0;
                background: linear-gradient(145deg, #0a3a0a 0%, #155f15 100%);
                color: #ffffff;
                border-radius: 5px 5px 0 0;
                border-bottom: 1px solid #1e5f1e;
            }}
            .content {{
                padding: 20px;
                color: #e6e6e6;
            }}
            .button {{
                display: inline-block;
                padding: 10px 20px;
                background: linear-gradient(90deg, #006400, #00a000);
                color: #ffffff;
                text-decoration: none;
                border-radius: 5px;
                margin-top: 20px;
                font-weight: bold;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            .footer {{
                text-align: center;
                padding: 20px;
                color: #a3ffa3;
                font-size: 12px;
                background-color: #061306;
                border-top: 1px solid #1e5f1e;
                border-radius: 0 0 5px 5px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>{title}</h2>
            </div>
            <div class="content">
                {content}
                {f'<p style="text-align: center;"><a href="{button_url}" class="button">{button_text}</a></p>' if button_text and button_url else ''}
            </div>
            <div class="footer">
                <p>This is an automated message from FundedWhales. Please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    """
    return html_template

def send_email(to_email, subject, body):
    from_email = "<EMAIL>"
    from_password = "Fundedwhales@9"

    msg = MIMEMultipart()
    msg["From"] = from_email
    msg["To"] = to_email
    msg["Subject"] = subject
    msg.attach(MIMEText(body, "html"))

    with smtplib.SMTP_SSL("smtp.hostinger.com", 465) as server:
        server.login(from_email, from_password)
        server.sendmail(from_email, to_email, msg.as_string())

async def get_complete_order_details(order_id: int, session: Session):
    """
    Helper function to get complete order details including all related information.
    This can be reused across multiple endpoints to ensure consistent response format.
    """
    order = session.get(OrderModel, order_id)
    if not order:
        return None

    # Get associated images
    images = session.query(OrderImage).filter(OrderImage.order_id == order_id).all()
    image_data = None
    if images:
        latest_image = max(images, key=lambda x: x.created_at)
        image_data = {
            "url": latest_image.image_url,
            "created_at": latest_image.created_at.isoformat()
        }

    # Get timeline events
    timeline_events = session.query(OrderTimeline).filter(
        OrderTimeline.order_id == order_id
    ).order_by(OrderTimeline.event_date.desc()).all()

    # Get pass order details if exists
    pass_order = session.query(PassOrder).filter(
        PassOrder.order_id == order_id
    ).first()

    # Get completed order details if exists
    completed_order = session.exec(
        select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)
    ).first()

    # Get order status if exists
    order_status = session.exec(
        select(OrderStatus).where(OrderStatus.order_id == order_id)
    ).first()

    # Get stage2 account if exists
    stage2_account = session.exec(
        select(Stage2Account).where(Stage2Account.order_id == order_id)
    ).first()

    # Get live account if exists
    live_account = session.exec(
        select(LiveAccount).where(LiveAccount.order_id == order_id)
    ).first()

    # Get fail order if exists
    fail_order = session.exec(
        select(FailOrder).where(FailOrder.order_id == order_id)
    ).first()

    # Determine status based on order state
    if fail_order:
        status = "Failed"
    elif live_account:
        status = "Live"
    elif stage2_account:
        status = "Stage Two"
    elif pass_order:
        status = "Passed"
    elif completed_order:
        status = "Active"
    else:
        status = "Pending"

    # Prepare the response
    response = {
        "id": order.id,
        "order_id": f"FW{order.id}",
        "username": order.username,
        "email": order.email,
        "challenge_type": order.challenge_type,
        "account_size": order.account_size,
        "platform": order.platform,
        "payment_method": order.payment_method,
        "txid": order.txid,
        "image": image_data,
        "status": status,
        "is_active": order_status.is_active if order_status else True,
        "timeline": [
            {
                "event_type": event.event_type,
                "event_date": event.event_date.isoformat(),
                "notes": event.notes
            } for event in timeline_events
        ]
    }

    # Add completed order details if exists
    if completed_order:
        response.update({
            "completed": True,
            "server": completed_order.server,
            "platform_login": completed_order.platform_login,
            "platform_password": completed_order.platform_password,
            "session_id": completed_order.session_id,
            "terminal_id": completed_order.terminal_id,
            "profit_target": completed_order.profit_target,
            "completed_at": completed_order.completed_at.isoformat() if hasattr(completed_order, 'completed_at') else None
        })
    else:
        response["completed"] = False

    # Add pass order details if exists
    if pass_order:
        response.update({
            "passed": True,
            "pass_order": {
                "pass_date": pass_order.pass_date.isoformat() if pass_order else None,
                "profit_amount": pass_order.profit_amount if pass_order else None,
                "notes": pass_order.notes if pass_order else None
            }
        })
    else:
        response["passed"] = False

    # Add stage2 account details if exists
    if stage2_account:
        response.update({
            "is_stage2": True,
            "stage2_account": {
                "server": stage2_account.server,
                "platform_login": stage2_account.platform_login,
                "platform_password": stage2_account.platform_password,
                "session_id": stage2_account.session_id,
                "terminal_id": stage2_account.terminal_id,
                "profit_target": stage2_account.profit_target,
                "status": stage2_account.status,
                "created_at": stage2_account.created_at.isoformat() if hasattr(stage2_account, 'created_at') else None
            }
        })
    else:
        response["is_stage2"] = False

    # Add live account details if exists
    if live_account:
        response.update({
            "is_live": True,
            "live_account": {
                "server": live_account.server,
                "platform_login": live_account.platform_login,
                "platform_password": live_account.platform_password,
                "session_id": live_account.session_id,
                "terminal_id": live_account.terminal_id,
                "profit_share": live_account.profit_share,
                "status": live_account.status,
                "created_at": live_account.created_at.isoformat() if hasattr(live_account, 'created_at') else None
            }
        })
    else:
        response["is_live"] = False

    # Add fail order details if exists
    if fail_order:
        response.update({
            "failed": True,
            "fail_order": {
                "reason": fail_order.reason,
                "failed_at": fail_order.failed_at.isoformat() if hasattr(fail_order, 'failed_at') else None
            }
        })
    else:
        response["failed"] = False

    return response

@order_router.post("/order")
async def create_order(
    challenge_type: str = Form(...),
    account_size: str = Form(...),
    platform: str = Form(...),
    payment_method: str = Form(...),
    txid: str = Form(...),
    current_user: User = Depends(get_current_user)
):
    # Check if an order with the same txid already exists
    with Session(engine) as session:
        existing_order = session.exec(select(OrderModel).where(OrderModel.txid == txid)).first()
        if existing_order:
            raise HTTPException(status_code=400, detail="Transaction ID already used. Please provide a unique transaction ID.")

    random_id = random.randint(********, ********)

    # Automatically use the logged-in user's email
    email = current_user.email

    # Create order with creation timestamp
    try:
        order = OrderModel(
            id=random_id,
            username=current_user.username,
            email=email,
            challenge_type=challenge_type,
            account_size=account_size,
            platform=platform,
            payment_method=payment_method,
            txid=txid,
            created_at=datetime.utcnow()
        )
    except Exception as e:
        # Fallback if created_at field is not in the database yet
        order = OrderModel(
            id=random_id,
            username=current_user.username,
            email=email,
            challenge_type=challenge_type,
            account_size=account_size,
            platform=platform,
            payment_method=payment_method,
            txid=txid
        )
        print(f"Warning: created_at field not used: {str(e)}")

    with Session(engine) as session:
        try:
            # First add and commit the order to get its ID
            session.add(order)
            session.commit()
            session.refresh(order)



            # Create timeline entry for order creation with timestamp
            current_time = datetime.utcnow()
            try:
                # Try to use order.created_at if it exists
                event_date = order.created_at if hasattr(order, 'created_at') and order.created_at else current_time
                timeline_entry = OrderTimeline(
                    order_id=order.id,
                    event_type="created",
                    event_date=event_date,
                    notes=f"Order created by {current_user.username}"
                )
            except Exception as e:
                # Fallback if there's an issue with created_at
                timeline_entry = OrderTimeline(
                    order_id=order.id,
                    event_type="created",
                    event_date=current_time,
                    notes=f"Order created by {current_user.username}"
                )
                print(f"Warning: using current time for timeline: {str(e)}")
            session.add(timeline_entry)

            datetime_model = DateTimeModel(
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(datetime_model)
            session.commit()

            # Send order confirmation email
            subject = "Order Confirmation - FundedWhales Trading Challenge"
            html_content = create_order_confirmation_email(
                current_user.username,
                order.id,
                challenge_type,
                account_size,
                platform
            )
            send_email(email, subject, html_content)

            # Check for giveaway eligibility and auto-enter user
            print(f"🎁 ORDER DEBUG: About to check giveaway eligibility")
            print(f"   User: {current_user.username} (ID: {current_user.id})")
            print(f"   Order ID: {order.id}")
            print(f"   Account Size: {account_size}")
            try:
                from routes.giveaway import auto_enter_giveaway
                print(f"   Calling auto_enter_giveaway...")
                auto_enter_giveaway(current_user, order.id, account_size, session)
                print(f"   ✅ Giveaway processing completed")
            except Exception as giveaway_error:
                print(f"   ❌ Error processing giveaway entry: {str(giveaway_error)}")
                import traceback
                traceback.print_exc()
                # Continue even if giveaway processing fails

            # Process affiliate commission if user was referred
            print(f"💰 AFFILIATE DEBUG: About to check affiliate commission")
            print(f"   User: {current_user.username} (ID: {current_user.id})")
            print(f"   Referred By: {current_user.referred_by}")
            try:
                from routes.affiliate import process_affiliate_commission

                # Parse account size to get order value
                order_value = 0.0
                if isinstance(account_size, str):
                    account_size_clean = account_size.replace('$', '').replace(',', '')
                    try:
                        order_value = float(account_size_clean)
                    except ValueError:
                        print(f"   ⚠️ Could not parse order value from: {account_size}")
                        order_value = 0.0
                else:
                    order_value = float(account_size)

                print(f"   Order Value: ${order_value}")
                print(f"   Calling process_affiliate_commission...")
                process_affiliate_commission(current_user, order.id, order_value, session)
                print(f"   ✅ Affiliate commission processing completed")
            except Exception as affiliate_error:
                print(f"   ❌ Error processing affiliate commission: {str(affiliate_error)}")
                import traceback
                traceback.print_exc()
                # Continue even if affiliate processing fails

            # Get complete order details to return
            order_details = await get_complete_order_details(random_id, session)

            # Add success message
            order_details["message"] = "Order created successfully"

            return order_details

        except Exception as e:
            session.rollback()
            raise HTTPException(status_code=500, detail=f"Error creating order: {str(e)}")

@order_router.delete("/order/{order_id}")
async def delete_order(order_id: int):
    with Session(engine) as session:
        try:
            # First, get the order to check if it exists
            order = session.get(OrderModel, order_id)
            if order is None:
                return {"error": "Order not found"}

            # Get associated images before deleting the order
            images = session.query(OrderImage).filter(OrderImage.order_id == order_id).all()

            # Delete images from Cloudinary and database
            for image in images:
                try:
                    # Delete from Cloudinary
                    cloudinary.uploader.destroy(image.cloudinary_public_id)
                except Exception as cloud_error:
                    print(f"Error deleting image from Cloudinary: {str(cloud_error)}")

                # Delete image record from database
                session.delete(image)

            # Delete timeline entries
            session.query(OrderTimeline).filter(OrderTimeline.order_id == order_id).delete()

            # Delete pass order if exists
            session.query(PassOrder).filter(PassOrder.order_id == order_id).delete()

            # Delete fail order if exists
            session.query(FailOrder).filter(FailOrder.order_id == order_id).delete()

            # Delete reject order if exists
            session.query(RejectOrder).filter(RejectOrder.order_id == order_id).delete()

            # Delete complete order if exists
            session.query(CompleteOrderModel).filter(CompleteOrderModel.order_id == order_id).delete()

            # Delete stage2 account if exists
            session.query(Stage2Account).filter(Stage2Account.order_id == order_id).delete()

            # Delete live account if exists
            session.query(LiveAccount).filter(LiveAccount.order_id == order_id).delete()

            # Delete order status if exists
            session.query(OrderStatus).filter(OrderStatus.order_id == order_id).delete()

            # Delete certificate if exists
            session.query(Certificate).filter(Certificate.order_id == order_id).delete()

            # Finally delete the order
            session.delete(order)
            session.commit()

            return {
                "message": "Order and associated images deleted successfully",
                "deleted_images_count": len(images)
            }
        except Exception as e:
            session.rollback()
            raise HTTPException(status_code=500, detail=f"Error deleting order: {str(e)}")

@order_router.get("/order/{order_id}")
async def get_order(
    order_id: int,
    session: Session = Depends(get_session)
):
    with Session(engine) as session:
        # Use the helper function to get complete order details
        order_details = await get_complete_order_details(order_id, session)

        if not order_details:
            raise HTTPException(status_code=404, detail="Order not found")

        return order_details

@order_router.get("/orders")
async def get_all_orders():
    with Session(engine) as session:
        orders = session.exec(select(OrderModel)).all()

        # Get complete details for each order
        result = []
        for order in orders:
            # Get complete order details using the helper function
            order_details = await get_complete_order_details(order.id, session)
            if order_details:
                result.append(order_details)

        return result

SQLModel.metadata.create_all(engine)

@order_router.post("/complete_order/{order_id}")
async def complete_order(
    order_id: int,
    server: str = Form(...),
    platform_login: str = Form(...),
    platform_password: str = Form(...),
    session_id: Optional[str] = Form(None),
    terminal_id: Optional[int] = Form(None),
    profit_target: Optional[int] = Form(None)
):
    with Session(engine) as session:
        # Check if order exists
        order = session.get(OrderModel, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        # Check if order is already completed
        existing_complete = session.exec(
            select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)
        ).first()
        if existing_complete:
            raise HTTPException(status_code=400, detail="Order has already been completed")

        # Create complete order record with completion timestamp
        current_time = datetime.utcnow()
        try:
            complete_order = CompleteOrderModel(
                order_id=order_id,
                server=server,
                platform_login=platform_login,
                platform_password=platform_password,
                session_id=session_id,
                terminal_id=terminal_id,
                profit_target=profit_target,
                completed_at=current_time
            )
        except Exception as e:
            # Fallback if completed_at field is not in the database yet
            complete_order = CompleteOrderModel(
                order_id=order_id,
                server=server,
                platform_login=platform_login,
                platform_password=platform_password,
                session_id=session_id,
                terminal_id=terminal_id,
                profit_target=profit_target
            )
            print(f"Warning: completed_at field not used: {str(e)}")

        session.add(complete_order)

        # Add timeline entry for order completion
        try:
            timeline_entry = OrderTimeline(
                order_id=order_id,
                event_type="completed",
                event_date=current_time,
                notes=f"Order completed with login {platform_login}"
            )
        except Exception as e:
            # Fallback if event_date field is not in the database yet
            timeline_entry = OrderTimeline(
                order_id=order_id,
                event_type="completed",
                notes=f"Order completed with login {platform_login}"
            )
            print(f"Warning: event_date field not used: {str(e)}")

        session.add(timeline_entry)

        session.commit()

        # Send email notification
        subject = "Your Trading Challenge Account is Ready"
        html_content = create_challenge_completion_email(
            order.username,
            order_id,
            server,
            platform_login,
            platform_password
        )
        send_email(order.email, subject, html_content)

        # Get complete order details to return
        order_details = await get_complete_order_details(order_id, session)

        # Add success message
        order_details["message"] = "Order completed successfully"
        order_details["complete_order_id"] = complete_order.id

        return order_details

@order_router.put("/edit_complete_order/{order_id}")
async def edit_complete_order(
    order_id: int,
    server: str = Form(...),
    platform_login: str = Form(...),
    platform_password: str = Form(...),
    session_id: Optional[str] = Form(None),
    terminal_id: Optional[int] = Form(None),
    profit_target: Optional[int] = Form(None)
):
    with Session(engine) as session:
        complete_order = session.exec(select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)).first()
        if complete_order is None:
            return {"error": "Completed order not found"}

        complete_order.server = server
        complete_order.platform_login = platform_login
        complete_order.platform_password = platform_password
        complete_order.session_id = session_id
        complete_order.terminal_id = terminal_id
        complete_order.profit_target = profit_target

        session.add(complete_order)
        session.commit()
        session.refresh(complete_order)

        # Get complete order details to return
        order_details = await get_complete_order_details(order_id, session)

        # Add success message
        order_details["message"] = "Completed order updated successfully"
        order_details["complete_order_id"] = complete_order.id

        return order_details

@order_router.get("/completed_orders")
async def get_all_completed_orders():
    with Session(engine) as session:
        completed_orders = session.exec(
            select(CompleteOrderModel)
        ).all()

        # Get complete details for each completed order
        result = []
        for completed_order in completed_orders:
            # Get complete order details using the helper function
            order_details = await get_complete_order_details(completed_order.order_id, session)
            if order_details:
                # Add the complete_order_id to the response
                order_details["complete_order_id"] = completed_order.id
                result.append(order_details)

        return result

@order_router.get("/running_orders")
async def get_running_orders():
    with Session(engine) as session:
        completed_orders = session.exec(select(CompleteOrderModel)).all()

        # Get complete details for each running order
        result = []
        for completed_order in completed_orders:
            order_id = completed_order.order_id

            # Check if order is passed or failed
            pass_order = session.exec(select(PassOrder).where(PassOrder.order_id == order_id)).first()
            fail_order = session.exec(select(FailOrder).where(FailOrder.order_id == order_id)).first()
            stage2_account = session.exec(select(Stage2Account).where(Stage2Account.order_id == order_id)).first()
            live_account = session.exec(select(LiveAccount).where(LiveAccount.order_id == order_id)).first()

            # Skip orders that are passed, failed, or moved to stage2/live
            if pass_order or fail_order or stage2_account or live_account:
                continue

            # Get complete order details using the helper function
            order_details = await get_complete_order_details(order_id, session)
            if order_details:
                result.append(order_details)

        return result

@order_router.get("/pending_orders")
async def get_pending_orders():
    """
    Get all pending orders (orders that have been created but not yet completed)
    """
    with Session(engine) as session:
        # Get all orders
        all_orders = session.exec(select(OrderModel)).all()

        # Get all completed orders
        completed_order_ids = [
            co.order_id for co in session.exec(select(CompleteOrderModel)).all()
        ]

        # Filter out orders that have been completed
        result = []
        for order in all_orders:
            if order.id not in completed_order_ids:
                # Get order details
                order_details = {
                    "id": order.id,
                    "order_id": f"FW{order.id}",
                    "username": order.username,
                    "email": order.email,
                    "challenge_type": order.challenge_type,
                    "account_size": order.account_size,
                    "platform": order.platform,
                    "payment_method": order.payment_method,
                    "txid": order.txid,
                    "status": "Pending",
                    "created_at": order.created_at.isoformat() if hasattr(order, 'created_at') and order.created_at else None
                }

                # Get associated images
                images = session.exec(select(OrderImage).where(OrderImage.order_id == order.id)).all()
                image_data = None
                if images:
                    latest_image = max(images, key=lambda x: x.created_at)
                    image_data = {
                        "url": latest_image.image_url,
                        "created_at": latest_image.created_at.isoformat()
                    }
                order_details["image"] = image_data

                # Get timeline events
                timeline_events = session.exec(
                    select(OrderTimeline)
                    .where(OrderTimeline.order_id == order.id)
                    .order_by(OrderTimeline.event_date.desc())
                ).all()

                order_details["timeline"] = [
                    {
                        "event_type": event.event_type,
                        "event_date": event.event_date.isoformat(),
                        "notes": event.notes
                    } for event in timeline_events
                ]

                result.append(order_details)

        return result

class LiveAccount(SQLModel, table=True):
    __tablename__ = "liveaccount"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    server: str
    platform_login: str
    platform_password: str
    session_id: Optional[str] = None
    terminal_id: Optional[int] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    profit_share: float = 80.0  # Default profit share percentage
    status: str = "active"  # active, inactive

class Stage2Account(SQLModel, table=True):
    __tablename__ = "stage2account"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    server: str
    platform_login: str
    platform_password: str
    session_id: Optional[str] = None
    terminal_id: Optional[int] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    profit_target: Optional[float] = None
    status: str = "active"  # active, completed, failed

SQLModel.metadata.create_all(engine)

@order_router.get("/order_ids")
async def get_order_ids(current_user: User = Depends(get_current_user)):
    with Session(engine) as session:
        # Get all orders for the current user
        orders = session.exec(
            select(OrderModel).where(OrderModel.username == current_user.username)
        ).all()

        result = []
        for order in orders:
            # Initialize status as None
            status = None

            # Check if order is failed
            fail_order = session.exec(
                select(FailOrder).where(FailOrder.order_id == order.id)
            ).first()
            if fail_order:
                status = "Failed"
            else:
                # Check if order has a live account
                live_account = session.exec(
                    select(LiveAccount).where(LiveAccount.order_id == order.id)
                ).first()
                if live_account:
                    status = "Live"
                else:
                    # Check if order has a stage 2 account
                    stage2_account = session.exec(
                        select(Stage2Account).where(Stage2Account.order_id == order.id)
                    ).first()
                    if stage2_account:
                        status = "Stage Two"
                    else:
                        # Check if order is completed
                        complete_order = session.exec(
                            select(CompleteOrderModel).where(CompleteOrderModel.order_id == order.id)
                        ).first()
                        if complete_order:
                            status = "Active"
                        else:
                            status = "Pending"

            result.append({
                "order_id": f"FW{order.id}",
                "balance": order.account_size,
                "username": order.username,
                "status": status,
                "created_at": order.created_at.isoformat() if hasattr(order, 'created_at') and order.created_at else None,
                "challenge_type": order.challenge_type,  # Account type
                "account_size": order.account_size
            })

        return result

@order_router.get("/my-account-details")
async def get_my_account_details(current_user: User = Depends(get_current_user)):
    """
    Get current user's account details with status-specific information:
    - Pending: status, account_size, challenge_type, created_at
    - Completed: + completed order credentials
    - Passed: + pass order credentials and details
    """
    with Session(engine) as session:
        # Get all orders for the current user
        orders = session.exec(
            select(OrderModel).where(OrderModel.username == current_user.username)
        ).all()

        if not orders:
            return {
                "message": "No orders found for current user",
                "orders": []
            }

        result = []
        for order in orders:
            order_detail = {
                "order_id": f"FW{order.id}",
                "username": order.username,
                "status": "Pending",
                "account_size": order.account_size,
                "challenge_type": order.challenge_type,
                "created_at": order.created_at.isoformat() if hasattr(order, 'created_at') and order.created_at else None,
                "platform": order.platform,
                "payment_method": order.payment_method
            }

            # Check if order is completed
            complete_order = session.exec(
                select(CompleteOrderModel).where(CompleteOrderModel.order_id == order.id)
            ).first()

            if complete_order:
                order_detail["status"] = "Completed"
                order_detail["completed_at"] = complete_order.created_at.isoformat() if hasattr(complete_order, 'created_at') and complete_order.created_at else None

                # Add completed order credentials if available
                order_detail["credentials"] = {
                    "server": getattr(complete_order, 'server', None),
                    "platform_login": getattr(complete_order, 'platform_login', None),
                    "platform_password": getattr(complete_order, 'platform_password', None)
                }

            # Check if order is passed
            pass_order = session.exec(
                select(PassOrder).where(PassOrder.order_id == order.id)
            ).first()

            if pass_order:
                order_detail["status"] = "Passed"
                order_detail["passed_at"] = pass_order.pass_date.isoformat() if pass_order.pass_date else None
                order_detail["profit_amount"] = pass_order.profit_amount
                order_detail["pass_notes"] = pass_order.notes

                # Add pass order credentials
                order_detail["pass_credentials"] = {
                    "server": pass_order.server,
                    "platform_login": pass_order.platform_login,
                    "platform_password": pass_order.platform_password,
                    "session_id": getattr(pass_order, 'session_id', None),
                    "terminal_id": getattr(pass_order, 'terminal_id', None)
                }

            # Check if order is in Stage 2
            stage2_account = session.exec(
                select(Stage2Account).where(Stage2Account.order_id == order.id)
            ).first()

            if stage2_account:
                order_detail["status"] = "Stage 2 Account"
                order_detail["stage2_created_at"] = stage2_account.created_at.isoformat() if hasattr(stage2_account, 'created_at') and stage2_account.created_at else None

                # Add stage 2 credentials
                order_detail["stage2_credentials"] = {
                    "server": stage2_account.server,
                    "platform_login": stage2_account.platform_login,
                    "platform_password": stage2_account.platform_password,
                    "session_id": stage2_account.session_id,
                    "terminal_id": stage2_account.terminal_id,
                    "profit_target": getattr(stage2_account, 'profit_target', None)
                }

            # Check if order is live
            live_account = session.exec(
                select(LiveAccount).where(LiveAccount.order_id == order.id)
            ).first()

            if live_account:
                order_detail["status"] = "Live Account"
                order_detail["live_created_at"] = live_account.created_at.isoformat() if hasattr(live_account, 'created_at') and live_account.created_at else None

                # Add live account credentials
                order_detail["live_credentials"] = {
                    "server": live_account.server,
                    "platform_login": live_account.platform_login,
                    "platform_password": live_account.platform_password,
                    "session_id": live_account.session_id,
                    "terminal_id": live_account.terminal_id,
                    "profit_target": getattr(live_account, 'profit_target', None)
                }

            # Check if order failed
            fail_order = session.exec(
                select(FailOrder).where(FailOrder.order_id == order.id)
            ).first()

            if fail_order:
                order_detail["status"] = "Failed"
                order_detail["failed_at"] = fail_order.fail_date.isoformat() if fail_order.fail_date else None
                order_detail["fail_reason"] = fail_order.reason
                order_detail["fail_notes"] = fail_order.notes

            result.append(order_detail)

        return {
            "user": {
                "username": current_user.username,
                "email": current_user.email,
                "name": current_user.name
            },
            "total_orders": len(result),
            "orders": result
        }

@order_router.get("/account_detail/{order_id}")
async def get_account_detail(order_id: int):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if order is None:
            return {"error": "Order not found"}

        order_detail = {
            "order_id": f"FW{order.id}",
            "challenge_type": order.challenge_type,
            "account_size": order.account_size,
            "platform": order.platform,
            "username": order.username
        }

        # Check for different order statuses
        fail_order = session.exec(select(FailOrder).where(FailOrder.order_id == order_id)).first()
        pass_order = session.exec(select(PassOrder).where(PassOrder.order_id == order_id)).first()
        live_account = session.exec(select(LiveAccount).where(LiveAccount.order_id == order_id)).first()
        stage2_account = session.exec(select(Stage2Account).where(Stage2Account.order_id == order_id)).first()
        completed_order = session.exec(select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)).first()

        # Determine status based on order state
        if fail_order:
            status = "Failed"
        elif live_account:
            status = "Live"
        elif stage2_account:
            status = "Stage Two"
        elif pass_order:
            status = "Passed"
        elif completed_order:
            status = "Active"
        else:
            status = "Pending"

        # Add completed order details if exists
        if completed_order:
            order_detail.update({
                "server": completed_order.server,
                "platform_login": completed_order.platform_login,
                "platform_password": completed_order.platform_password,
                "session_id": completed_order.session_id,
                "terminal_id": completed_order.terminal_id,
                "profit_target": completed_order.profit_target
            })

        # Set the status
        order_detail["status"] = status

        return order_detail

@order_router.get("/order_details/{order_id}")
async def get_order_details(order_id: int):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if order is None:
            return {"error": "Order not found"}

        order_details = {
            "id": f"FW{order.id}",
            "challenge_type": order.challenge_type,
            "account_size": order.account_size,
            "platform": order.platform,
            "username": order.username
        }

        # Check for different order statuses
        fail_order = session.exec(select(FailOrder).where(FailOrder.order_id == order_id)).first()
        pass_order = session.exec(select(PassOrder).where(PassOrder.order_id == order_id)).first()
        live_account = session.exec(select(LiveAccount).where(LiveAccount.order_id == order_id)).first()
        stage2_account = session.exec(select(Stage2Account).where(Stage2Account.order_id == order_id)).first()
        completed_order = session.exec(select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)).first()

        # Determine status based on order state
        if fail_order:
            status = "Failed"
        elif live_account:
            status = "Live"
        elif stage2_account:
            status = "Stage Two"
        elif pass_order:
            status = "Passed"
        elif completed_order:
            status = "Active"
        else:
            status = "Pending"

        # Add completed order details if exists
        if completed_order:
            order_details.update({
                "server": completed_order.server,
                "platform_login": completed_order.platform_login,
                "platform_password": completed_order.platform_password,
                "session_id": completed_order.session_id,
                "terminal_id": completed_order.terminal_id,
                "profit_target": completed_order.profit_target
            })

        # Set the status
        order_details["status"] = status

        return order_details



@order_router.get("/stats")
async def get_stats(session: Session = Depends(get_session)):
    # Total Users
    total_users = len(session.exec(select(User)).all())

    # Orders Created
    total_orders = len(session.exec(select(OrderModel)).all())

    # Completed Orders
    completed_orders = len(session.exec(select(CompleteOrderModel)).all())

    # Passed Orders
    passed_orders = len(session.exec(select(PassOrder)).all())

    # Failed Orders
    failed_orders = len(session.exec(select(FailOrder)).all())

    # Stage 2 Accounts
    stage2_accounts = len(session.exec(select(Stage2Account)).all())

    # Live Accounts
    live_accounts = len(session.exec(select(LiveAccount)).all())

    # Running Orders (completed but not passed/failed/stage2/live)
    running_orders = completed_orders - (passed_orders + failed_orders + stage2_accounts + live_accounts)
    if running_orders < 0:
        running_orders = 0  # Handle potential overlap cases

    # Pending Orders (created but not yet completed)
    pending_orders = total_orders - completed_orders

    return {
        "total_users": total_users,
        "total_orders": total_orders,
        "completed_orders": completed_orders,
        "passed_orders": passed_orders,
        "failed_orders": failed_orders,
        "stage2_accounts": stage2_accounts,
        "live_accounts": live_accounts,
        "running_orders": running_orders,
        "pending_orders": pending_orders
    }

SQLModel.metadata.create_all(engine)

@order_router.post("/order_status/{order_id}")
async def update_order_status(
        order_id: int,
        is_active: bool = Form(...),
        status: str = Form(...)
    ):
        with Session(engine) as session:
            order = session.get(OrderModel, order_id)
            if order is None:
                return {"error": "Order not found"}

            order_status = OrderStatus(
                order_id=order_id,
                is_active=is_active,
                status=status
            )
            session.add(order_status)
            session.commit()
            session.refresh(order_status)

            # Get complete order details to return
            order_details = await get_complete_order_details(order_id, session)

            # Add success message
            order_details["message"] = "Order status updated successfully"
            order_details["order_status_id"] = order_status.id

            return order_details

@order_router.get("/order_status/{order_id}")
async def get_order_status(order_id: int):
        with Session(engine) as session:
            order_status = session.exec(select(OrderStatus).where(OrderStatus.order_id == order_id)).first()
            if order_status is None:
                return {"error": "Order status not found"}
            return {
                "order_id": f"FW{order_status.order_id}",
                "is_active": order_status.is_active,
                "status": order_status.status
            }
SQLModel.metadata.create_all(engine)
@order_router.post("/reject_order/{order_id}")
async def reject_order(
            order_id: int,
            reason: str = Form(...)
        ):
            with Session(engine) as session:
                order = session.get(OrderModel, order_id)
                if order is None:
                    return {"error": "Order not found"}

                current_time = datetime.utcnow()
                try:
                    reject_order = RejectOrder(
                        order_id=order_id,
                        reason=reason,
                        rejected_at=current_time
                    )
                except Exception as e:
                    # Fallback if rejected_at field is not in the database yet
                    reject_order = RejectOrder(
                        order_id=order_id,
                        reason=reason
                    )
                    print(f"Warning: rejected_at field not used: {str(e)}")

                session.add(reject_order)

                # Add timeline entry for order rejection
                try:
                    timeline_entry = OrderTimeline(
                        order_id=order_id,
                        event_type="rejected",
                        event_date=current_time,
                        notes=f"Order rejected: {reason}"
                    )
                except Exception as e:
                    # Fallback if event_date field is not in the database yet
                    timeline_entry = OrderTimeline(
                        order_id=order_id,
                        event_type="rejected",
                        notes=f"Order rejected: {reason}"
                    )
                    print(f"Warning: event_date field not used: {str(e)}")

                session.add(timeline_entry)

                session.commit()
                session.refresh(reject_order)

                # Get complete order details to return
                order_details = await get_complete_order_details(order_id, session)

                # Add success message
                order_details["message"] = "Order rejected successfully"
                order_details["reject_order_id"] = reject_order.id

                return order_details

@order_router.get("/reject_order/{order_id}")
async def get_reject_order(order_id: int):
            with Session(engine) as session:
                reject_order = session.exec(select(RejectOrder).where(RejectOrder.order_id == order_id)).first()
                if reject_order is None:
                    return {"error": "Rejected order not found"}

                order = session.get(OrderModel, order_id)
                if order is None:
                    return {"error": "Order not found"}

                return {
                    "order_id": f"FW{order.id}",
                    "username": order.username,
                    "email": order.email,
                    "challenge_type": order.challenge_type,
                    "account_size": order.account_size,
                    "platform": order.platform,
                    "payment_method": order.payment_method,
                    "txid": order.txid,
                    "reason": reject_order.reason
                }


class FailureReason(str, Enum):
    DAILY_DRAWDOWN_5 = "Policy Violation Detection - 5% Daily Drawdown Exceeded"
    OVERALL_DRAWDOWN = "Breach Alert - Overall Drawdown Limit Violated"
    PHASE1_TRADING_DAYS = "Trading Breach - Phase-1 Minimum Trading Days Not Met"
    DAILY_DRAWDOWN_4 = "Policy Violation Detection - 4% Daily Drawdown Exceeded"
    PHASE2_TRADING_DAYS = "Trading Breach - Phase-2 Minimum Trading Days Requirement Unfulfilled"

@order_router.post("/fail_order/{order_id}")
async def fail_order(
    order_id: int,
    reason: str = Form(...),
    date_time: str = Form(None)
):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        current_time = datetime.utcnow()
        try:
            fail_order = FailOrder(
                order_id=order_id,
                reason=reason,
                failed_at=current_time
            )
        except Exception as e:
            # Fallback if failed_at field is not in the database yet
            fail_order = FailOrder(
                order_id=order_id,
                reason=reason
            )
            print(f"Warning: failed_at field not used: {str(e)}")

        session.add(fail_order)

        # Add timeline entry for order failure
        try:
            timeline_entry = OrderTimeline(
                order_id=order_id,
                event_type="failed",
                event_date=current_time,
                notes=f"Order failed: {reason}"
            )
        except Exception as e:
            # Fallback if event_date field is not in the database yet
            timeline_entry = OrderTimeline(
                order_id=order_id,
                event_type="failed",
                notes=f"Order failed: {reason}"
            )
            print(f"Warning: event_date field not used: {str(e)}")

        session.add(timeline_entry)

        session.commit()
        session.refresh(fail_order)

        # Get complete order details to return before sending email
        order_details = await get_complete_order_details(order_id, session)

        # Add success message
        order_details["message"] = "Order failed successfully"
        order_details["fail_order_id"] = fail_order.id

        # Send email notification
        subject = "Important Update: Trading Challenge Status"
        html_content = create_fail_notification_email(
            order.username,
            order_id,
            reason
        )
        send_email(order.email, subject, html_content)

        return order_details

@order_router.get("/rejected_orders")
async def get_all_rejected_orders():
    with Session(engine) as session:
        rejected_orders = session.exec(
            select(RejectOrder, OrderModel)
            .join(OrderModel, RejectOrder.order_id == OrderModel.id)
        ).all()
        return [
            {
                "reject_order_id": rejected_order[0].id,
                "order_id": f"FW{rejected_order[0].order_id}",
                "username": rejected_order[1].username,
                "email": rejected_order[1].email,
                "challenge_type": rejected_order[1].challenge_type,
                "account_size": rejected_order[1].account_size,
                "platform": rejected_order[1].platform,
                "payment_method": rejected_order[1].payment_method,
                "txid": rejected_order[1].txid,
                "reason": rejected_order[0].reason
            }
            for rejected_order in rejected_orders
        ]

@order_router.get("/failed_orders")
async def get_all_failed_orders():
    with Session(engine) as session:
        failed_orders = session.exec(
            select(FailOrder, OrderModel, CompleteOrderModel)
            .join(OrderModel, FailOrder.order_id == OrderModel.id)
            .outerjoin(CompleteOrderModel, FailOrder.order_id == CompleteOrderModel.order_id)
        ).all()
        return [
            {
                "fail_order_id": failed_order[0].id,
                "order_id": f"FW{failed_order[0].order_id}",
                "username": failed_order[1].username,
                "email": failed_order[1].email,
                "challenge_type": failed_order[1].challenge_type,
                "account_size": failed_order[1].account_size,
                "platform": failed_order[1].platform,
                "payment_method": failed_order[1].payment_method,
                "txid": failed_order[1].txid,
                "reason": failed_order[0].reason,
                "server": failed_order[2].server if failed_order[2] else None,
                "platform_login": failed_order[2].platform_login if failed_order[2] else None,
                "platform_password": failed_order[2].platform_password if failed_order[2] else None,
                "image": {
                    "image_url": failed_order[1].images[0].image_url if failed_order[1].images else None,
                    "created_at": failed_order[1].images[0].created_at if failed_order[1].images else None
                } if failed_order[1].images else None
            }
            for failed_order in failed_orders
        ]

@order_router.post("/pass_order/{order_id}")
async def pass_order(
    order_id: int,
    profit_amount: Optional[float] = Form(None),
    notes: Optional[str] = Form(None)
):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if order is None:
            return {"error": "Order not found"}

        # Check if order is already passed
        existing_pass_order = session.exec(select(PassOrder).where(PassOrder.order_id == order_id)).first()
        if existing_pass_order:
            return {"error": "Order has already been passed"}

        # Check if order is already failed
        existing_fail_order = session.exec(select(FailOrder).where(FailOrder.order_id == order_id)).first()
        if existing_fail_order:
            return {"error": "Cannot pass a failed order"}

        # Create pass order record with pass date
        current_time = datetime.utcnow()
        try:
            pass_order = PassOrder(
                order_id=order_id,
                profit_amount=profit_amount,
                notes=notes,
                pass_date=current_time
            )
        except Exception as e:
            # Fallback if pass_date field is not in the database yet
            pass_order = PassOrder(
                order_id=order_id,
                profit_amount=profit_amount,
                notes=notes
            )
            print(f"Warning: pass_date field not used: {str(e)}")

        session.add(pass_order)

        # Update order status if it exists
        order_status = session.exec(select(OrderStatus).where(OrderStatus.order_id == order_id)).first()
        if order_status:
            order_status.status = "pass"
            session.add(order_status)
        else:
            # Create new order status
            new_status = OrderStatus(
                order_id=order_id,
                is_active=True,
                status="pass"
            )
            session.add(new_status)

        session.commit()
        session.refresh(pass_order)

        # Send email notification
        subject = "Congratulations! Your Challenge Has Been Passed"
        html_content = create_pass_notification_email(
            order.username,
            order_id,
            profit_amount
        )
        send_email(order.email, subject, html_content)

        # Add timeline entry with the same timestamp
        try:
            timeline_entry = OrderTimeline(
                order_id=order_id,
                event_type="passed",
                event_date=current_time,
                notes=f"Order passed with profit amount: {profit_amount}" if profit_amount else "Order passed"
            )
        except Exception as e:
            # Fallback if event_date field is not in the database yet
            timeline_entry = OrderTimeline(
                order_id=order_id,
                event_type="passed",
                notes=f"Order passed with profit amount: {profit_amount}" if profit_amount else "Order passed"
            )
            print(f"Warning: event_date field not used: {str(e)}")

        session.add(timeline_entry)
        session.commit()
        session.refresh(pass_order)

        # Get complete order details to return
        order_details = await get_complete_order_details(order_id, session)

        # Add success message
        order_details["message"] = "Order passed successfully"
        order_details["pass_order_id"] = pass_order.id

        return order_details

@order_router.get("/pass_order/{order_id}")
async def get_pass_order(order_id: int):
    with Session(engine) as session:
        pass_order = session.exec(select(PassOrder).where(PassOrder.order_id == order_id)).first()
        if pass_order is None:
            return {"passed": False}

        return {"passed": True, "pass_date": pass_order.pass_date, "profit_amount": pass_order.profit_amount, "notes": pass_order.notes}

@order_router.get("/passed_orders")
async def get_all_passed_orders():
    with Session(engine) as session:
        passed_orders = session.exec(
            select(PassOrder, OrderModel)
            .join(OrderModel, PassOrder.order_id == OrderModel.id)
        ).all()
        return [
            {
                "pass_order_id": passed_order[0].id,
                "order_id": f"FW{passed_order[0].order_id}",
                "username": passed_order[1].username,
                "email": passed_order[1].email,
                "challenge_type": passed_order[1].challenge_type,
                "account_size": passed_order[1].account_size,
                "platform": passed_order[1].platform,
                "pass_date": passed_order[0].pass_date,
                "profit_amount": passed_order[0].profit_amount,
                "notes": passed_order[0].notes
            }
            for passed_order in passed_orders
        ]

SQLModel.metadata.create_all(engine)

class AccountType(str, Enum):
    STAGE2 = "stage2"
    LIVE = "live"

@order_router.put("/edit_passed_order/{order_id}")
async def edit_passed_order(
    order_id: int,
    server: str = Form(...),
    platform_login: str = Form(...),
    platform_password: str = Form(...),
    session_id: Optional[str] = Form(None),
    terminal_id: Optional[int] = Form(None),
    account_type: AccountType = Form(...),
    profit_target: Optional[float] = Form(None)
):
    with Session(engine) as session:
        # First check if order exists
        order = session.get(OrderModel, order_id)
        if not order:
            return {"error": "Order not found"}

        # Check if order is passed
        pass_order = session.exec(
            select(PassOrder)
            .where(PassOrder.order_id == order_id)
        ).first()

        # If order is not passed, check if it's completed
        if not pass_order:
            complete_order = session.exec(
                select(CompleteOrderModel)
                .where(CompleteOrderModel.order_id == order_id)
            ).first()
            if not complete_order:
                return {"error": "Order must be passed before creating Stage2 or Live account"}

        # Check if order is failed
        fail_order = session.exec(
            select(FailOrder)
            .where(FailOrder.order_id == order_id)
        ).first()
        if fail_order:
            return {"error": "Cannot create account for failed order"}

        # Update or create CompleteOrder record
        complete_order = session.exec(
            select(CompleteOrderModel)
            .where(CompleteOrderModel.order_id == order_id)
        ).first()

        if complete_order:
            complete_order.server = server
            complete_order.platform_login = platform_login
            complete_order.platform_password = platform_password
            complete_order.session_id = session_id
            complete_order.terminal_id = terminal_id
            session.add(complete_order)
        else:
            complete_order = CompleteOrderModel(
                order_id=order_id,
                server=server,
                platform_login=platform_login,
                platform_password=platform_password,
                session_id=session_id,
                terminal_id=terminal_id
            )
            session.add(complete_order)

        # Create Stage2 or Live account based on account_type
        if account_type == AccountType.STAGE2:
            # Check if Stage2 account already exists
            existing_stage2 = session.exec(
                select(Stage2Account)
                .where(Stage2Account.order_id == order_id)
            ).first()
            if existing_stage2:
                return {"error": "Stage2 account already exists for this order"}

            # Check if Live account exists (can't go back to Stage2)
            existing_live = session.exec(
                select(LiveAccount)
                .where(LiveAccount.order_id == order_id)
            ).first()
            if existing_live:
                return {"error": "Cannot create Stage2 account - order already has Live account"}

            # Create Stage2 account with current timestamp
            current_time = datetime.utcnow()
            stage2_account = Stage2Account(
                order_id=order_id,
                server=server,
                platform_login=platform_login,
                platform_password=platform_password,
                session_id=session_id,
                terminal_id=terminal_id,
                profit_target=profit_target,
                created_at=current_time
            )
            session.add(stage2_account)

            # Add timeline entry for stage2 creation
            timeline_entry = OrderTimeline(
                order_id=order_id,
                event_type="stage2_created",
                event_date=current_time,
                notes=f"Stage 2 account created with login {platform_login}"
            )
            session.add(timeline_entry)

            # Remove from passed orders if it exists
            if pass_order:
                session.delete(pass_order)

            # Send email notification for Stage2 account creation
            subject = "Stage 2 Account Created"
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Your Stage 2 Account Details</title>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Arial, sans-serif; background-color: #0d1117; color: #e6e6e6;">
                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                    <tr>
                        <td style="padding: 0;">
                            <table role="presentation" cellpadding="0" cellspacing="0" width="650" style="border-collapse: collapse; margin: 0 auto; background: linear-gradient(145deg, #0a1a30 0%, #0d2240 100%); border: 1px solid #1e3a5f;">

                                <!-- Header with Logo -->
                                <tr>
                                    <td style="padding: 25px 30px 15px; text-align: center;">
                                        <div style="display: inline-block; background: linear-gradient(145deg, #0a2240 0%, #153a6a 100%); padding: 20px; border-radius: 15px; box-shadow: 0 0 25px rgba(255, 130, 0, 0.3);">
                                            <h1 style="color: #ffffff; margin: 0; font-size: 32px; letter-spacing: 2px; text-transform: uppercase; font-weight: 800;">FX<span style="color: #ff8200;">ENTRA</span></h1>
                                            <p style="color: #a3c2ff; margin: 5px 0 0; font-size: 14px; letter-spacing: 3px; text-transform: uppercase;">PROP TRADING</p>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Stage 2 Account Banner -->
                                <tr>
                                    <td style="padding: 0; position: relative;">
                                        <div style="background-color: #061325; height: 120px; position: relative; overflow: hidden;">
                                            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(145deg, rgba(10, 34, 64, 0.85) 0%, rgba(21, 58, 106, 0.85) 100%); display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
                                                <div style="background: rgba(6, 19, 37, 0.7); padding: 20px 30px; border-radius: 10px; border: 1px solid rgba(255, 130, 0, 0.3);">
                                                    <h2 style="color: #ffffff; margin: 0; font-size: 24px; letter-spacing: 1px; text-transform: uppercase; font-weight: 700;">STAGE 2 ACCOUNT DETAILS</h2>
                                                    <p style="color: #a3c2ff; margin: 10px 0 0; font-size: 16px; letter-spacing: 1px;">ORDER #: FW{order_id}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Main Content -->
                                <tr>
                                    <td style="padding: 30px 30px 20px;">
                                        <h3 style="color: #ff8200; margin: 0 0 15px; font-size: 22px; letter-spacing: 1px;">Dear {order.username},</h3>

                                        <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">Congratulations on passing your challenge! Your Stage 2 account has been created successfully. Below are your account credentials:</p>

                                        <!-- Account Details Box -->
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0; background: linear-gradient(145deg, #0a1a30 0%, #0d2240 100%); border-radius: 10px; border: 1px solid #1e3a5f;">
                                            <tr>
                                                <td style="padding: 0;">
                                                    <!-- Header -->
                                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                        <tr>
                                                            <td style="background: linear-gradient(90deg, #ff8200, #ff9a40); padding: 12px 20px; border-radius: 10px 10px 0 0;">
                                                                <h4 style="color: #ffffff; margin: 0; font-size: 18px; text-transform: uppercase; letter-spacing: 1px;">Account Credentials</h4>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <!-- Content -->
                                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                        <tr>
                                                            <td style="padding: 20px;">
                                                                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                    <tr>
                                                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 58, 95, 0.5);">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3c2ff; font-size: 16px;">Server:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{server}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 58, 95, 0.5);">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3c2ff; font-size: 16px;">Login:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{platform_login}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 58, 95, 0.5);">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3c2ff; font-size: 16px;">Password:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{platform_password}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 58, 95, 0.5);">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3c2ff; font-size: 16px;">Session ID:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{session_id}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="padding: 10px 0;">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3c2ff; font-size: 16px;">Terminal ID:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{terminal_id}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    {f'''
                                                                    <tr>
                                                                        <td style="padding: 10px 0;">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3c2ff; font-size: 16px;">Profit Target:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">${profit_target:,.2f}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    ''' if profit_target else ''}
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>

                                        <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">You can now start trading with your Stage 2 account. Remember to follow the trading rules and aim for consistent profits.</p>

                                        <!-- View Details CTA -->
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0;">
                                            <tr>
                                                <td style="text-align: center;">
                                                    <a href="https://www.fundedwhales.com/order/{order_id}" style="display: inline-block; background: linear-gradient(90deg, #ff8200, #ff9a40); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 5px; font-weight: bold; font-size: 16px; text-transform: uppercase; letter-spacing: 1px;">VIEW ORDER DETAILS</a>
                                                </td>
                                            </tr>
                                        </table>

                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin-top: 25px;">
                                            <tr>
                                                <td style="padding: 0;">
                                                    <p style="color: #e6e6e6; margin: 0; font-size: 16px; line-height: 1.6;">Best Regards,</p>
                                                    <p style="color: #ff8200; margin: 10px 0 0; font-size: 18px; font-weight: bold;">The Funded Horizon Team</p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>

                                <!-- Footer -->
                                <tr>
                                    <td style="background-color: #061325; padding: 20px; border-top: 1px solid #1e3a5f;">
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                            <tr>
                                                <td style="text-align: center; color: #a3c2ff; font-size: 14px;">
                                                    <p style="margin: 0 0 10px;">© 2025 Funded Horizon Prop Trading. All rights reserved.</p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </body>
            </html>
            """
            send_email(order.email, subject, body="Stage 2 account created successfully", html_content=html_content)

        elif account_type == AccountType.LIVE:
            # Check if Live account already exists
            existing_live = session.exec(
                select(LiveAccount)

                .where(LiveAccount.order_id == order_id)
            ).first()
            if existing_live:
                return {"error": "Live account already exists for this order"}

            # Create Live account with current timestamp
            current_time = datetime.utcnow()
            live_account = LiveAccount(
                order_id=order_id,
                server=server,
                platform_login=platform_login,
                platform_password=platform_password,
                session_id=session_id,
                terminal_id=terminal_id,
                created_at=current_time
            )
            session.add(live_account)

            # Add timeline entry for live account creation
            timeline_entry = OrderTimeline(
                order_id=order_id,
                event_type="live_created",
                event_date=current_time,
                notes=f"Live account created with login {platform_login}"
            )
            session.add(timeline_entry)

            # Remove from passed orders if it exists
            if pass_order:
                session.delete(pass_order)

            # Send email notification for Live account creation
            subject = "Live Account Created"
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Your Live Trading Account Details</title>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Arial, sans-serif; background-color: #0a1a0a; color: #e6e6e6;">
                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                    <tr>
                        <td style="padding: 0;">
                            <table role="presentation" cellpadding="0" cellspacing="0" width="650" style="border-collapse: collapse; margin: 0 auto; background: linear-gradient(145deg, #0a2a0a 0%, #0d3d0d 100%); border: 1px solid #1e5f1e;">

                                <!-- Header with Logo -->
                                <tr>
                                    <td style="padding: 25px 30px 15px; text-align: center;">
                                        <div style="display: inline-block; background: linear-gradient(145deg, #0a3a0a 0%, #155f15 100%); padding: 20px; border-radius: 15px; box-shadow: 0 0 25px rgba(0, 130, 0, 0.3);">
                                            <h1 style="color: #ffffff; margin: 0; font-size: 32px; letter-spacing: 2px; text-transform: uppercase; font-weight: 800;">FX<span style="color: #00c800;">ENTRA</span></h1>
                                            <p style="color: #a3ffa3; margin: 5px 0 0; font-size: 14px; letter-spacing: 3px; text-transform: uppercase;">PROP TRADING</p>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Live Account Banner -->
                                <tr>
                                    <td style="padding: 0; position: relative;">
                                        <div style="background-color: #061306; height: 120px; position: relative; overflow: hidden;">
                                            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(145deg, rgba(10, 64, 10, 0.85) 0%, rgba(21, 106, 21, 0.85) 100%); display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
                                                <div style="background: rgba(6, 37, 6, 0.7); padding: 20px 30px; border-radius: 10px; border: 1px solid rgba(77, 255, 77, 0.3);">
                                                    <h2 style="color: #4dff4d; margin: 0; font-size: 24px; letter-spacing: 1px; text-transform: uppercase; font-weight: 700;">LIVE ACCOUNT DETAILS</h2>
                                                    <p style="color: #a3ffa3; margin: 10px 0 0; font-size: 16px; letter-spacing: 1px;">ORDER #: FW{order_id}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Main Content -->
                                <tr>
                                    <td style="padding: 30px 30px 20px;">
                                        <h3 style="color: #00c800; margin: 0 0 15px; font-size: 22px; letter-spacing: 1px;">Dear {order.username},</h3>

                                        <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">Congratulations on your outstanding performance! Your Live trading account has been created successfully. Below are your account credentials:</p>

                                        <!-- Account Details Box -->
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0; background: linear-gradient(145deg, #0a2a0a 0%, #0d3d0d 100%); border-radius: 10px; border: 1px solid #1e5f1e;">
                                            <tr>
                                                <td style="padding: 0;">
                                                    <!-- Header -->
                                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                        <tr>
                                                            <td style="background: linear-gradient(90deg, #00a000, #00cc00); padding: 12px 20px; border-radius: 10px 10px 0 0;">
                                                                <h4 style="color: #ffffff; margin: 0; font-size: 18px; text-transform: uppercase; letter-spacing: 1px;">Account Credentials</h4>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <!-- Content -->
                                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                        <tr>
                                                            <td style="padding: 20px;">
                                                                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                    <tr>
                                                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3ffa3; font-size: 16px;">Server:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{server}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3ffa3; font-size: 16px;">Login:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{platform_login}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3ffa3; font-size: 16px;">Password:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{platform_password}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3ffa3; font-size: 16px;">Session ID:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{session_id}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="padding: 10px 0;">
                                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                                <tr>
                                                                                    <td width="40%" style="color: #a3ffa3; font-size: 16px;">Terminal ID:</td>
                                                                                    <td width="60%" style="color: #ffffff; font-size: 16px;">{terminal_id}</td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>

                                        <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">You are now trading with a Live account! You'll receive 80% of the profits you generate. We look forward to a successful partnership.</p>

                                        <!-- View Details CTA -->
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0;">
                                            <tr>
                                                <td style="text-align: center;">
                                                    <a href="https://www.fundedwhales.com/order/{order_id}" style="display: inline-block; background: linear-gradient(90deg, #006400, #00a000); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 5px; font-weight: bold; font-size: 16px; text-transform: uppercase; letter-spacing: 1px;">VIEW ORDER DETAILS</a>
                                                </td>
                                            </tr>
                                        </table>

                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin-top: 25px;">
                                            <tr>
                                                <td style="padding: 0;">
                                                    <p style="color: #e6e6e6; margin: 0; font-size: 16px; line-height: 1.6;">Best Regards,</p>
                                                    <p style="color: #00c800; margin: 10px 0 0; font-size: 18px; font-weight: bold;">The FundedWhales Team</p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>

                                <!-- Footer -->
                                <tr>
                                    <td style="background-color: #061306; padding: 20px; border-top: 1px solid #1e5f1e;">
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                            <tr>
                                                <td style="text-align: center; color: #a3ffa3; font-size: 14px;">
                                                    <p style="margin: 0 0 10px;">© 2025 FundedWhales Prop Trading. All rights reserved.</p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </body>
            </html>
            """
            send_email(order.email, subject, body="Live account created successfully", html_content=html_content)

        try:
            session.commit()
            return {
                "message": f"Order updated successfully and {account_type.value} account created",
                "account_type": account_type.value,
                "order_id": f"FW{order_id}",
                "platform_login": platform_login
            }
        except Exception as e:
            session.rollback()
            return {"error": f"Failed to update order: {str(e)}"}

        # This code is unreachable due to the return statement above
        # Keeping it commented for reference
        # # Add timeline entry based on account type
        # if account_type == AccountType.STAGE2:
        #     timeline_entry = OrderTimeline(
        #         order_id=order_id,
        #         event_type="stage2_created",
        #         notes=f"Stage 2 account created with login {platform_login}"
        #     )
        # else:
        #     timeline_entry = OrderTimeline(
        #         order_id=order_id,
        #         event_type="live_created",
        #         notes=f"Live account created with login {platform_login}"
        #     )
        #
        # session.add(timeline_entry)
        # session.commit()

@order_router.get("/stage2_accounts")
async def get_stage2_accounts():
    with Session(engine) as session:
        stage2_accounts = session.exec(
            select(Stage2Account, OrderModel)
            .join(OrderModel, Stage2Account.order_id == OrderModel.id)
        ).all()

        return [
            {
                "id": account[0].id,
                "order_id": f"FW{account[0].order_id}",
                "username": account[1].username,
                "email": account[1].email,
                "challenge_type": account[1].challenge_type,
                "account_size": account[1].account_size,
                "platform": account[1].platform,
                "server": account[0].server,
                "platform_login": account[0].platform_login,
                "platform_password": account[0].platform_password,
                "session_id": account[0].session_id,
                "terminal_id": account[0].terminal_id,
                "created_at": account[0].created_at,
                "profit_target": account[0].profit_target,
                "status": account[0].status
            }
            for account in stage2_accounts
        ]

@order_router.get("/live_accounts")
async def get_live_accounts():
    with Session(engine) as session:
        live_accounts = session.exec(
            select(LiveAccount, OrderModel)
            .join(OrderModel, LiveAccount.order_id == OrderModel.id)
        ).all()

        return [
            {
                "live_account_id": live[0].id,
                "order_id": f"FW{live[0].order_id}",
                "username": live[1].username,
                "email": live[1].email,
                "challenge_type": live[1].challenge_type,
                "account_size": live[1].account_size,
                "platform": live[1].platform,
                "created_at": live[0].created_at.isoformat()
            }
            for live in live_accounts
        ]

@order_router.get("/order_timeline/{order_id}")
async def get_order_timeline(order_id: int):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if order is None:
            return {"error": "Order not found"}

        timeline_entries = session.exec(
            select(OrderTimeline)
            .where(OrderTimeline.order_id == order_id)
            .order_by(OrderTimeline.event_date)
        ).all()

        return {
            "order_id": f"FW{order_id}",
            "username": order.username,
            "timeline": [
                {
                    "event_type": entry.event_type,
                    "event_date": entry.event_date,
                    "notes": entry.notes
                }
                for entry in timeline_entries
            ]
        }

@order_router.get("/certificates")
async def get_all_certificates(session: Session = Depends(get_session)):
    certificates = session.exec(
        select(Certificate)
        .join(OrderModel)
        .order_by(Certificate.issue_date.desc())
        ).all()

    result = []
    for cert in certificates:
        order = session.get(OrderModel, cert.order_id)
        result.append({
            "certificate_number": cert.certificate_number,
            "order_id": f"FW{cert.order_id}",
            "username": order.username,
            "issue_date": cert.issue_date.strftime("%Y-%m-%d"),
            "account_size": cert.account_size,
            "challenge_type": cert.challenge_type,
            "profit_target": cert.profit_target
        })

    return result

@order_router.get("/certificate/{order_id}")
async def get_certificate(
    order_id: int,
    session: Session = Depends(get_session)
):
    certificate = session.exec(
        select(Certificate)
        .where(Certificate.order_id == order_id)
        ).first()

    if not certificate:
        return {"error": "Certificate not found"}

    order = session.get(OrderModel, order_id)
    return {
        "certificate_number": certificate.certificate_number,
        "order_id": f"FW{order_id}",
        "username": order.username,
        "issue_date": certificate.issue_date.strftime("%Y-%m-%d"),
        "account_size": certificate.account_size,
        "challenge_type": certificate.challenge_type,
        "profit_target": certificate.profit_target
    }

SQLModel.metadata.create_all(engine)

@order_router.post("/certificate/{order_id}")
async def create_certificate(
    order_id: int,
    certificate_number: str = Form(...),
    account_size: str = Form(...),
    challenge_type: str = Form(...),
    profit_target: Optional[float] = Form(None)
):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        certificate = Certificate(
            order_id=order_id,
            certificate_number=certificate_number,
            account_size=account_size,
            challenge_type=challenge_type,
            profit_target=profit_target
        )
        session.add(certificate)
        session.commit()
        session.refresh(certificate)

        return {"message": "Certificate created successfully", "certificate_id": certificate.id}

@order_router.post("/live_account/{order_id}")
async def create_live_account(
    order_id: int,
    server: str = Form(...),
    platform_login: str = Form(...),
    platform_password: str = Form(...),
    session_id: str = Form(...),
    terminal_id: int = Form(...),
    profit_target: Optional[float] = Form(None)
):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        # Check if live account already exists
        existing_live = session.exec(
            select(LiveAccount).where(LiveAccount.order_id == order_id)
        ).first()
        if existing_live:
            raise HTTPException(status_code=400, detail="Live account already exists for this order")

        live_account = LiveAccount(
            order_id=order_id,
            server=server,
            platform_login=platform_login,
            platform_password=platform_password,
            session_id=session_id,
            terminal_id=terminal_id
        )
        session.add(live_account)

        # Create timeline entry
        timeline_entry = OrderTimeline(
            order_id=order_id,
            event_type="live_created",
            notes=f"Live account created for order #{order_id}"
        )
        session.add(timeline_entry)

        # Remove from passed orders if it exists
        pass_order = session.exec(
            select(PassOrder)
            .where(PassOrder.order_id == order_id)
        ).first()
        if pass_order:
            session.delete(pass_order)

        session.commit()

        # Send email notification for Live account creation
        subject = "Your Live Trading Account is Ready"
        html_content = create_live_account_email(
            order.username,
            order_id,
            server,
            platform_login,
            platform_password,
            live_account.profit_share
        )
        send_email(order.email, subject, html_content)

        return {"message": "Live account created successfully", "live_account_id": live_account.id}

@order_router.get("/live_accounts")
async def get_all_live_accounts():
    with Session(engine) as session:
        live_accounts = session.exec(
            select(LiveAccount, OrderModel)
            .join(OrderModel, LiveAccount.order_id == OrderModel.id)
        ).all()
        return [
            {
                "live_account_id": live[0].id,
                "order_id": f"FW{live[0].order_id}",
                "username": live[1].username,
                "email": live[1].email,
                "challenge_type": live[1].challenge_type,
                "account_size": live[1].account_size,
                "platform": live[1].platform,
                "created_at": live[0].created_at.isoformat()
            }
            for live in live_accounts
        ]

@order_router.post("/stage2_account/{order_id}")
async def create_stage2_account(
    order_id: int,
    server: str = Form(...),
    platform_login: str = Form(...),
    platform_password: str = Form(...),
    session_id: str = Form(...),
    terminal_id: int = Form(...),
    profit_target: Optional[float] = Form(None)
):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        # Check if stage 2 account already exists
        existing_stage2 = session.exec(
            select(Stage2Account).where(Stage2Account.order_id == order_id)
        ).first()
        if existing_stage2:
            raise HTTPException(status_code=400, detail="Stage 2 account already exists for this order")

        stage2_account = Stage2Account(
            order_id=order_id,
            server=server,
            platform_login=platform_login,
            platform_password=platform_password,
            session_id=session_id,
            terminal_id=terminal_id,
            profit_target=profit_target
        )
        session.add(stage2_account)

        # Create timeline entry
        timeline_entry = OrderTimeline(
            order_id=order_id,
            event_type="stage2_created",
            notes=f"Stage 2 account created for order #{order_id}"
        )
        session.add(timeline_entry)

        # Remove from passed orders if it exists
        pass_order = session.exec(
            select(PassOrder)
            .where(PassOrder.order_id == order_id)
        ).first()
        if pass_order:
            session.delete(pass_order)

        session.commit()

        # Send email notification for Stage2 account creation
        subject = "Stage 2 Account Created"
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Your Stage 2 Account Details</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Arial, sans-serif; background-color: #0a1a0a; color: #e6e6e6;">
            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                <tr>
                    <td style="padding: 0;">
                        <table role="presentation" cellpadding="0" cellspacing="0" width="650" style="border-collapse: collapse; margin: 0 auto; background: linear-gradient(145deg, #0a2a0a 0%, #0d3d0d 100%); border: 1px solid #1e5f1e;">

                            <!-- Header with Logo -->
                            <tr>
                                <td style="padding: 25px 30px 15px; text-align: center;">
                                    <div style="display: inline-block; background: linear-gradient(145deg, #0a3a0a 0%, #155f15 100%); padding: 20px; border-radius: 15px; box-shadow: 0 0 25px rgba(0, 130, 0, 0.3);">
                                        <h1 style="color: #ffffff; margin: 0; font-size: 32px; letter-spacing: 2px; text-transform: uppercase; font-weight: 800;">FX<span style="color: #00c800;">ENTRA</span></h1>
                                        <p style="color: #a3ffa3; margin: 5px 0 0; font-size: 14px; letter-spacing: 3px; text-transform: uppercase;">PROP TRADING</p>
                                    </div>
                                </td>
                            </tr>

                            <!-- Stage 2 Account Banner -->
                            <tr>
                                <td style="padding: 0; position: relative;">
                                    <div style="background-color: #061306; height: 120px; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(145deg, rgba(10, 64, 10, 0.85) 0%, rgba(21, 106, 21, 0.85) 100%); display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
                                            <div style="background: rgba(6, 37, 6, 0.7); padding: 20px 30px; border-radius: 10px; border: 1px solid rgba(0, 130, 0, 0.3);">
                                                <h2 style="color: #ffffff; margin: 0; font-size: 24px; letter-spacing: 1px; text-transform: uppercase; font-weight: 700;">STAGE 2 ACCOUNT DETAILS</h2>
                                                <p style="color: #a3ffa3; margin: 10px 0 0; font-size: 16px; letter-spacing: 1px;">ORDER #: FW{order_id}</p>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>

                            <!-- Main Content -->
                            <tr>
                                <td style="padding: 30px 30px 20px;">
                                    <h3 style="color: #00c800; margin: 0 0 15px; font-size: 22px; letter-spacing: 1px;">Dear {order.username},</h3>

                                    <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">Congratulations on passing your challenge! Your Stage 2 account has been created successfully. Below are your account credentials:</p>

                                    <!-- Account Details Box -->
                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0; background: linear-gradient(145deg, #0a2a0a 0%, #0d3d0d 100%); border-radius: 10px; border: 1px solid #1e5f1e;">
                                        <tr>
                                            <td style="padding: 0;">
                                                <!-- Header -->
                                                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                    <tr>
                                                        <td style="background: linear-gradient(90deg, #006400, #00a000); padding: 12px 20px; border-radius: 10px 10px 0 0;">
                                                            <h4 style="color: #ffffff; margin: 0; font-size: 18px; text-transform: uppercase; letter-spacing: 1px;">Account Credentials</h4>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <!-- Content -->
                                                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                    <tr>
                                                        <td style="padding: 20px;">
                                                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                <tr>
                                                                    <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                            <tr>
                                                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Server:</td>
                                                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{server}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                            <tr>
                                                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Login:</td>
                                                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{platform_login}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                            <tr>
                                                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Password:</td>
                                                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{platform_password}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                            <tr>
                                                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Session ID:</td>
                                                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{session_id}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td style="padding: 10px 0;">
                                                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                            <tr>
                                                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Terminal ID:</td>
                                                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{terminal_id}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                {f'''
                                                                <tr>
                                                                    <td style="padding: 10px 0;">
                                                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                                                            <tr>
                                                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Profit Target:</td>
                                                                                <td width="60%" style="color: #ffffff; font-size: 16px;">${profit_target:,.2f}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                ''' if profit_target else ''}
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>

                                    <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">You can now start trading with your Stage 2 account. Remember to follow the trading rules and aim for consistent profits.</p>

                                    <!-- View Details CTA -->
                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0;">
                                        <tr>
                                            <td style="text-align: center;">
                                                <a href="https://www.fundedwhales.com/order/{order_id}" style="display: inline-block; background: linear-gradient(90deg, #006400, #00a000); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 5px; font-weight: bold; font-size: 16px; text-transform: uppercase; letter-spacing: 1px;">VIEW ORDER DETAILS</a>
                                            </td>
                                        </tr>
                                    </table>

                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin-top: 25px;">
                                        <tr>
                                            <td style="padding: 0;">
                                                <p style="color: #e6e6e6; margin: 0; font-size: 16px; line-height: 1.6;">Best Regards,</p>
                                                <p style="color: #00c800; margin: 10px 0 0; font-size: 18px; font-weight: bold;">The FundedWhales Team</p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>

                            <!-- Footer -->
                            <tr>
                                <td style="background-color: #061306; padding: 20px; border-top: 1px solid #1e5f1e;">
                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                        <tr>
                                            <td style="text-align: center; color: #a3ffa3; font-size: 14px;">
                                                <p style="margin: 0 0 10px;">© 2025 FundedWhales Prop Trading. All rights reserved.</p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </body>
        </html>
        """
        send_email(order.email, subject, body="Stage 2 account created successfully", html_content=html_content)

        return {"message": "Stage 2 account created successfully", "stage2_account_id": stage2_account.id}

@order_router.get("/stage2_accounts")
async def get_all_stage2_accounts():
    with Session(engine) as session:
        stage2_accounts = session.exec(
            select(Stage2Account, OrderModel)
            .join(OrderModel, Stage2Account.order_id == OrderModel.id)
        ).all()
        return [
            {
                "stage2_account_id": stage2[0].id,
                "order_id": f"FW{stage2[0].order_id}",
                "username": stage2[1].username,
                "email": stage2[1].email,
                "challenge_type": stage2[1].challenge_type,
                "account_size": stage2[1].account_size,
                "platform": stage2[1].platform,
                "server": stage2[0].server,
                "platform_login": stage2[0].platform_login,
                "platform_password": stage2[0].platform_password,
                "session_id": stage2[0].session_id,
                "terminal_id": stage2[0].terminal_id,
                "created_at": stage2[0].created_at.isoformat(),
                "profit_target": stage2[0].profit_target,
                "status": stage2[0].status
            }
            for stage2 in stage2_accounts
        ]

@order_router.get("/account_status/{order_id}")
async def get_account_status(order_id: int):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        # Check various statuses
        live_account = session.exec(select(LiveAccount).where(LiveAccount.order_id == order_id)).first()
        stage2_account = session.exec(select(Stage2Account).where(Stage2Account.order_id == order_id)).first()
        complete_order = session.exec(select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)).first()
        fail_order = session.exec(select(FailOrder).where(FailOrder.order_id == order_id)).first()
        pass_order = session.exec(select(PassOrder).where(PassOrder.order_id == order_id)).first()

        status = {
            "order_id": f"FW{order_id}",
            "is_live": bool(live_account),
            "is_stage2": bool(stage2_account),
            "is_complete": bool(complete_order),
            "is_failed": bool(fail_order),
            "is_passed": bool(pass_order),
            "current_status": "Pending"
        }

        # Determine current status
        if live_account:
            status["current_status"] = "Live"
        elif stage2_account:
            status["current_status"] = "Stage Two"
        elif fail_order:
            status["current_status"] = "Failed"
        elif pass_order:
            status["current_status"] = "Passed"
        elif complete_order:
            status["current_status"] = "Active"

        return status

@order_router.get("/order_timeline/{order_id}")
async def get_order_timeline(order_id: int):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        timeline_events = session.exec(
            select(OrderTimeline)
            .where(OrderTimeline.order_id == order_id)
            .order_by(OrderTimeline.event_date.desc())
        ).all()

        return [
            {
                "event_type": event.event_type,
                "event_date": event.event_date.isoformat(),
                "notes": event.notes
            }
            for event in timeline_events
        ]

@order_router.post("/send_certificate/{order_id}")
async def send_certificate(order_id: int):
    with Session(engine) as session:
        order = session.get(OrderModel, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        # Get pass order details for profit amount
        pass_order = session.exec(
            select(PassOrder).where(PassOrder.order_id == order_id)
        ).first()

        # Generate random certificate ID
        certificate_id = f"FH-{random.randint(********, ********)}-{datetime.now().year}"
        issue_date = datetime.now().strftime("%B %d, %Y")

        # Create certificate record
        certificate = Certificate(
            order_id=order_id,
            certificate_number=certificate_id,
            account_size=order.account_size,
            challenge_type=order.challenge_type,
            profit_target=8.0
        )
        session.add(certificate)
        session.commit()
        session.refresh(certificate)

        # Send email using new template
        subject = "Your FundedWhales Trading Excellence Certificate"
        html_content = create_certificate_email(
            username=order.username,
            order_id=order_id,
            challenge_type=order.challenge_type,
            account_size=order.account_size,
            certificate_id=certificate_id,
            issue_date=issue_date,
            profit_amount=pass_order.profit_amount if pass_order else None
        )
        
        send_email(order.email, subject, html_content)

        return {"message": "Certificate sent successfully", "certificate_id": certificate.id}


