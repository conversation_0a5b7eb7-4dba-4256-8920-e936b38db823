"""
Test script for the giveaway system
This script tests the complete giveaway functionality including:
1. Creating giveaways
2. Automatic user entry on qualifying orders
3. Email notifications
4. Winner selection
"""

import requests
import json
from datetime import datetime, timedelta

# Base URL for your API
BASE_URL = "http://localhost:8000"  # Adjust this to your actual API URL

def test_create_giveaway():
    """Test creating a new giveaway"""
    print("=== Testing Giveaway Creation ===")
    
    # First, login to get a token (you'll need valid credentials)
    login_data = {
        "username": "<EMAIL>",  # Replace with admin email
        "password": "admin_password"  # Replace with admin password
    }
    
    login_response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if login_response.status_code != 200:
        print("❌ Admin login failed. Please update credentials in the script.")
        return None
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Create giveaway
    giveaway_data = {
        "title": "Test $100K Account Giveaway",
        "description": "Test giveaway for the giveaway system",
        "prize_description": "$100,000 Funded Trading Account",
        "prize_value": 100000.0,
        "min_account_size": 50000.0,
        "start_date": datetime.utcnow().isoformat(),
        "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat(),
        "max_entries": 100,
        "terms_and_conditions": "Test terms and conditions"
    }
    
    response = requests.post(f"{BASE_URL}/giveaways/admin/create", json=giveaway_data, headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        giveaway = response.json()
        print(f"✅ Giveaway created successfully!")
        print(f"   ID: {giveaway['id']}")
        print(f"   Title: {giveaway['title']}")
        print(f"   Prize: {giveaway['prize_description']}")
        return giveaway, token
    else:
        print(f"❌ Failed to create giveaway: {response.json()}")
        return None, token

def test_get_active_giveaways():
    """Test getting active giveaways"""
    print("\n=== Testing Get Active Giveaways ===")
    
    response = requests.get(f"{BASE_URL}/giveaways/")
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        giveaways = response.json()
        print(f"✅ Found {len(giveaways)} active giveaways:")
        for giveaway in giveaways:
            print(f"   - {giveaway['title']} (ID: {giveaway['id']})")
            print(f"     Prize: {giveaway['prize_description']}")
            print(f"     Min Account: ${giveaway['min_account_size']:,.0f}")
            print(f"     Entries: {giveaway['total_entries']}")
        return giveaways
    else:
        print(f"❌ Failed to get giveaways: {response.json()}")
        return []

def test_user_order_and_giveaway_entry():
    """Test creating an order that should trigger giveaway entry"""
    print("\n=== Testing Order Creation and Giveaway Entry ===")
    
    # Login as a regular user
    user_login_data = {
        "username": "<EMAIL>",  # Replace with test user email
        "password": "testpassword"  # Replace with test user password
    }
    
    login_response = requests.post(f"{BASE_URL}/auth/login", data=user_login_data)
    if login_response.status_code != 200:
        print("❌ User login failed. Please create a test user first.")
        return False
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Create an order with qualifying account size
    order_data = {
        "email": "<EMAIL>",
        "challenge_type": "Phase 1 Challenge",
        "account_size": "$75,000",  # This should qualify for giveaway
        "platform": "MetaTrader 5",
        "payment_method": "Cryptocurrency",
        "txid": f"test_txid_{datetime.now().timestamp()}"
    }
    
    response = requests.post(f"{BASE_URL}/order/order", data=order_data, headers=headers)
    print(f"Order Creation Status Code: {response.status_code}")
    
    if response.status_code == 200:
        order = response.json()
        print(f"✅ Order created successfully!")
        print(f"   Order ID: {order.get('id', 'N/A')}")
        print(f"   Account Size: {order_data['account_size']}")
        print("   User should be automatically entered in giveaway")
        return True, token
    else:
        print(f"❌ Failed to create order: {response.json()}")
        return False, None

def test_user_giveaway_entries(token):
    """Test getting user's giveaway entries"""
    print("\n=== Testing User Giveaway Entries ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/giveaways/my-entries", headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        entries = response.json()
        print(f"✅ User has {len(entries)} giveaway entries:")
        for entry in entries:
            print(f"   - Giveaway: {entry['giveaway']['title']}")
            print(f"     Account Size: ${entry['account_size']:,.0f}")
            print(f"     Entry Date: {entry['entry_date']}")
            print(f"     Status: {entry['status']}")
        return entries
    else:
        print(f"❌ Failed to get user entries: {response.json()}")
        return []

def test_select_winner(giveaway_id, admin_token):
    """Test selecting a winner for the giveaway"""
    print(f"\n=== Testing Winner Selection for Giveaway {giveaway_id} ===")
    
    headers = {"Authorization": f"Bearer {admin_token}"}
    response = requests.post(f"{BASE_URL}/giveaways/admin/{giveaway_id}/select-winner", headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Winner selected successfully!")
        print(f"   Winner: {result['winner_username']} ({result['winner_email']})")
        print(f"   Giveaway: {result['giveaway_title']}")
        print(f"   Prize: {result['prize']}")
        return result
    else:
        print(f"❌ Failed to select winner: {response.json()}")
        return None

def test_email_previews():
    """Test email preview endpoints"""
    print("\n=== Testing Email Previews ===")
    
    # Test giveaway entry email
    response = requests.get(f"{BASE_URL}/email-preview/giveaway-entry")
    if response.status_code == 200:
        print("✅ Giveaway entry email preview works")
    else:
        print("❌ Giveaway entry email preview failed")
    
    # Test giveaway winner email
    response = requests.get(f"{BASE_URL}/email-preview/giveaway-winner")
    if response.status_code == 200:
        print("✅ Giveaway winner email preview works")
    else:
        print("❌ Giveaway winner email preview failed")
    
    print(f"🔗 View email previews at: {BASE_URL}/email-preview/")

def main():
    """Main test function"""
    print("🎁 Starting Giveaway System Tests")
    print("=" * 50)
    
    # Test 1: Get active giveaways
    active_giveaways = test_get_active_giveaways()
    
    # Test 2: Create a new giveaway (admin only)
    # giveaway, admin_token = test_create_giveaway()
    
    # Test 3: Test user order creation and automatic giveaway entry
    order_success, user_token = test_user_order_and_giveaway_entry()
    
    if order_success and user_token:
        # Test 4: Check user's giveaway entries
        user_entries = test_user_giveaway_entries(user_token)
    
    # Test 5: Test email previews
    test_email_previews()
    
    # Test 6: Select winner (uncomment if you want to test this)
    # if active_giveaways and admin_token:
    #     test_select_winner(active_giveaways[0]['id'], admin_token)
    
    print("\n" + "=" * 50)
    print("🎁 Giveaway System Tests Completed")
    print("\n📋 Summary:")
    print("   ✅ The giveaway system should automatically:")
    print("   1. Enter users in giveaways when they order $50K+ accounts")
    print("   2. Send giveaway entry notification emails")
    print("   3. Track all entries and allow winner selection")
    print("   4. Send winner notification emails")
    print("\n🔗 Test the email templates at:")
    print(f"   {BASE_URL}/email-preview/")

if __name__ == "__main__":
    main()
