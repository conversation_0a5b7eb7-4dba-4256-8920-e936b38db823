from sqlmodel import SQLModel
from pydantic import EmailStr
from pydantic import ConfigDict  # Import for Pydantic v2 configuration
from typing import Optional

class UserCreate(SQLModel):
    username: str
    email: EmailStr
    password: str
    name: str
    phone_no: str
    country: str
    address: str
    referral_code: Optional[str] = None  # Referral code of the person who referred this user

class UserLogin(SQLModel):
    email: EmailStr
    password: str

class UserResponse(SQLModel):
    id: int
    username: str
    email: EmailStr
    name: str
    country: str
    phone_no: str
    address: str
    hashed_password: str
    referral_code: Optional[str] = None  # User's own referral code (only if access enabled)
    referral_code_access_enabled: bool = False
    points_balance: int

    model_config = ConfigDict(from_attributes=True)  # Pydantic v2 compatibility for orm_mode

class UserWithVerification(UserResponse):
    is_verified: bool = False

    model_config = ConfigDict(from_attributes=True)

class Token(SQLModel):
    access_token: str
    token_type: str
    is_verified: bool = False
