"""
Test script for the referral system
This script tests the referral system functionality including:
1. User registration with referral codes
2. Points awarding
3. Transaction tracking
"""

import requests
import json
from datetime import datetime

# Base URL for your API
BASE_URL = "http://localhost:8000"  # Adjust this to your actual API URL

def test_user_registration_without_referral():
    """Test user registration without referral code"""
    print("=== Testing User Registration Without Referral ===")
    
    user_data = {
        "username": "testuser1",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "name": "Test User 1",
        "phone_no": "+1234567890",
        "country": "USA",
        "address": "123 Test Street"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=user_data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 200:
        user_info = response.json()
        print(f"User created successfully with referral code: {user_info.get('referral_code', 'N/A')}")
        return user_info
    else:
        print("Failed to create user")
        return None

def test_user_registration_with_referral(referral_code):
    """Test user registration with referral code"""
    print(f"\n=== Testing User Registration With Referral Code: {referral_code} ===")
    
    user_data = {
        "username": "testuser2",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "name": "Test User 2",
        "phone_no": "+1234567891",
        "country": "USA",
        "address": "456 Test Avenue",
        "referral_code": referral_code
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=user_data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 200:
        user_info = response.json()
        print(f"User created successfully with referral code: {user_info.get('referral_code', 'N/A')}")
        return user_info
    else:
        print("Failed to create user with referral")
        return None

def test_login_and_get_token(email, password):
    """Login and get access token"""
    print(f"\n=== Testing Login for {email} ===")
    
    login_data = {
        "username": email,  # Note: API expects email in username field
        "password": password
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        token_info = response.json()
        print("Login successful")
        return token_info["access_token"]
    else:
        print(f"Login failed: {response.json()}")
        return None

def test_points_balance(token):
    """Test getting points balance"""
    print("\n=== Testing Points Balance ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/points/balance", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        balance_info = response.json()
        print(f"Points Balance: {balance_info}")
        return balance_info
    else:
        print(f"Failed to get balance: {response.json()}")
        return None

def test_transaction_history(token):
    """Test getting transaction history"""
    print("\n=== Testing Transaction History ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/points/transactions", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        transactions = response.json()
        print(f"Transaction History ({len(transactions)} transactions):")
        for tx in transactions:
            print(f"  - {tx['transaction_type']}: {tx['points_amount']} points - {tx['description']}")
        return transactions
    else:
        print(f"Failed to get transactions: {response.json()}")
        return None

def test_referral_stats(token):
    """Test getting referral statistics"""
    print("\n=== Testing Referral Statistics ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/points/referrals", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"Referral Stats: {stats}")
        return stats
    else:
        print(f"Failed to get referral stats: {response.json()}")
        return None

def test_task_completion(token):
    """Test awarding points for task completion"""
    print("\n=== Testing Task Completion Points ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    params = {
        "task_name": "Complete Profile",
        "points": 25
    }
    
    response = requests.post(f"{BASE_URL}/points/task-completion", headers=headers, params=params)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Task completion result: {result}")
        return result
    else:
        print(f"Failed to award task points: {response.json()}")
        return None

def main():
    """Main test function"""
    print("Starting Referral System Tests")
    print("=" * 50)
    
    # Test 1: Create first user without referral
    user1 = test_user_registration_without_referral()
    if not user1:
        print("Cannot continue tests - first user creation failed")
        return
    
    # Test 2: Create second user with referral code from first user
    referral_code = user1.get("referral_code")
    if referral_code:
        user2 = test_user_registration_with_referral(referral_code)
    else:
        print("No referral code found for first user")
        return
    
    # Test 3: Login as first user and check points balance (should have 50 points from referral)
    token1 = test_login_and_get_token("<EMAIL>", "testpassword123")
    if token1:
        test_points_balance(token1)
        test_transaction_history(token1)
        test_referral_stats(token1)
        test_task_completion(token1)
        
        # Check balance again after task completion
        print("\n=== Points Balance After Task Completion ===")
        test_points_balance(token1)
    
    # Test 4: Login as second user and check points balance (should have 0 points)
    if user2:
        token2 = test_login_and_get_token("<EMAIL>", "testpassword123")
        if token2:
            test_points_balance(token2)
            test_transaction_history(token2)
            test_referral_stats(token2)
    
    print("\n" + "=" * 50)
    print("Referral System Tests Completed")

if __name__ == "__main__":
    main()
