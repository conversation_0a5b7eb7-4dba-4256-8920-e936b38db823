#!/usr/bin/env python3
"""
Test script for the secure affiliate system.
Tests the new admin endpoints and user restrictions.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"  # Adjust if your app runs on different port

def test_user_registration():
    """Test that new users don't get referral codes automatically"""
    print("🧪 Testing user registration (no auto referral code)...")
    
    user_data = {
        "username": "testuser_secure",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "name": "Test User Secure",
        "phone_no": "+1234567890",
        "country": "USA",
        "address": "123 Test Street"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/signup", json=user_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            user_info = response.json()
            referral_code = user_info.get('referral_code')
            access_enabled = user_info.get('referral_code_access_enabled', False)
            
            print(f"   Referral Code: {referral_code}")
            print(f"   Access Enabled: {access_enabled}")
            
            if referral_code is None and not access_enabled:
                print("   ✅ PASS: No referral code generated, access disabled")
                return user_info
            else:
                print("   ❌ FAIL: User got referral code or access automatically")
                return None
        else:
            print(f"   ❌ FAIL: Registration failed - {response.json()}")
            return None
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return None

def test_user_login_and_profile(email, password):
    """Test user login and profile access"""
    print("🧪 Testing user login and profile...")
    
    try:
        # Login
        login_data = {"username": email, "password": password}
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        
        if response.status_code != 200:
            print(f"   ❌ FAIL: Login failed - {response.json()}")
            return None
        
        token_data = response.json()
        token = token_data["access_token"]
        print("   ✅ Login successful")
        
        # Get profile
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/auth/user/me", headers=headers)
        
        if response.status_code == 200:
            profile = response.json()
            referral_code = profile.get('referral_code')
            access_enabled = profile.get('referral_code_access_enabled', False)
            
            print(f"   Referral Code in Profile: {referral_code}")
            print(f"   Access Enabled: {access_enabled}")
            
            if referral_code is None and not access_enabled:
                print("   ✅ PASS: Profile correctly hides referral code")
            else:
                print("   ❌ FAIL: Profile shows referral code when access disabled")
            
            return token
        else:
            print(f"   ❌ FAIL: Profile access failed - {response.json()}")
            return None
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return None

def test_affiliate_stats_restriction(token):
    """Test that affiliate stats are restricted"""
    print("🧪 Testing affiliate stats restriction...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/affiliate/stats", headers=headers)
        
        if response.status_code == 403:
            error_detail = response.json().get("detail", "")
            if "referral code access not enabled" in error_detail.lower():
                print("   ✅ PASS: Affiliate stats correctly restricted")
                return True
            else:
                print(f"   ❌ FAIL: Wrong error message - {error_detail}")
                return False
        else:
            print(f"   ❌ FAIL: Expected 403, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def test_admin_endpoints():
    """Test admin endpoints (requires admin user)"""
    print("🧪 Testing admin endpoints...")
    print("   ⚠️  Note: This requires an admin user to be set up")
    
    admin_email = input("   Enter admin email (or press Enter to skip): ").strip()
    
    if not admin_email:
        print("   ⏭️  Skipping admin endpoint tests")
        return
    
    admin_password = input("   Enter admin password: ").strip()
    
    try:
        # Login as admin
        login_data = {"username": admin_email, "password": admin_password}
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        
        if response.status_code != 200:
            print(f"   ❌ FAIL: Admin login failed - {response.json()}")
            return
        
        token_data = response.json()
        admin_token = token_data["access_token"]
        print("   ✅ Admin login successful")
        
        # Test first-order-users endpoint
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = requests.get(f"{BASE_URL}/admin/first-order-users", headers=headers)
        
        if response.status_code == 200:
            users = response.json()
            print(f"   ✅ First-order users endpoint works ({len(users)} users)")
        else:
            print(f"   ❌ FAIL: First-order users endpoint failed - {response.json()}")
        
        # Test referral access history
        response = requests.get(f"{BASE_URL}/admin/referral-access-history", headers=headers)
        
        if response.status_code == 200:
            history = response.json()
            print(f"   ✅ Referral access history endpoint works")
        else:
            print(f"   ❌ FAIL: Referral access history failed - {response.json()}")
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")

def main():
    """Main test function"""
    print("🚀 Testing Secure Affiliate System")
    print("=" * 40)
    
    # Test 1: User registration
    user_info = test_user_registration()
    if not user_info:
        print("❌ Registration test failed, stopping tests")
        sys.exit(1)
    
    # Test 2: User login and profile
    token = test_user_login_and_profile("<EMAIL>", "testpassword123")
    if not token:
        print("❌ Login test failed, stopping tests")
        sys.exit(1)
    
    # Test 3: Affiliate stats restriction
    test_affiliate_stats_restriction(token)
    
    # Test 4: Admin endpoints (optional)
    test_admin_endpoints()
    
    print("\n" + "=" * 40)
    print("🎉 Secure Affiliate System Tests Completed!")
    print("\n📋 Summary:")
    print("   ✅ New users don't get automatic referral codes")
    print("   ✅ User profiles hide referral codes when access disabled")
    print("   ✅ Affiliate endpoints are properly restricted")
    print("   ✅ Admin endpoints are available (if admin user exists)")
    
    print("\n🔧 Next Steps:")
    print("   1. Create an admin user using: python create_admin_user.py")
    print("   2. Test the complete workflow:")
    print("      - User registers → Creates order → Admin grants access")
    print("   3. Test referral functionality after access is granted")

if __name__ == "__main__":
    main()
