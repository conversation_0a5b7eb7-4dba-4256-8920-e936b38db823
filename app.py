import os
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.responses import HTMLResponse
import uvicorn

# Import the main application
try:
    from hello import app as fastapi_app
except ImportError:
    # If hello.py is not found, create a simple FastAPI app
    fastapi_app = FastAPI(title="FundedWhales Trading Platform")

# Add a landing page to the FastAPI app
@fastapi_app.get("/landing", response_class=HTMLResponse)
async def landing_page():
    """
    Landing page for the Hugging Face Space deployment
    """
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>FundedWhales Trading Platform</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8f9fa;
            }
            .header {
                background: linear-gradient(145deg, #0a3a0a 0%, #155f15 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                margin-bottom: 30px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                text-align: center;
            }
            h1 {
                margin: 0;
                font-size: 2.5rem;
            }
            .logo-text {
                font-size: 3rem;
                font-weight: 800;
                letter-spacing: 2px;
            }
            .highlight {
                color: #00c800;
            }
            .tagline {
                color: #a3ffa3;
                margin: 10px 0 0;
                font-size: 1.2rem;
                letter-spacing: 3px;
                text-transform: uppercase;
            }
            .content {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
            }
            .section {
                margin-bottom: 30px;
            }
            h2 {
                color: #155f15;
                border-bottom: 2px solid #e0e0e0;
                padding-bottom: 10px;
            }
            ul {
                padding-left: 20px;
            }
            li {
                margin-bottom: 10px;
            }
            .feature {
                font-weight: bold;
                color: #155f15;
            }
            .api-section {
                background: #f5f5f5;
                padding: 20px;
                border-radius: 5px;
                margin-top: 20px;
            }
            .endpoint {
                font-family: monospace;
                background: #e0e0e0;
                padding: 5px 10px;
                border-radius: 3px;
            }
            .footer {
                text-align: center;
                margin-top: 50px;
                color: #666;
                font-size: 0.9rem;
            }
            .button {
                display: inline-block;
                background: #155f15;
                color: white;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin-top: 10px;
                transition: background 0.3s;
            }
            .button:hover {
                background: #0a3a0a;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="logo-text">FUNDED<span class="highlight">WHALES</span></div>
            <p class="tagline">PROP TRADING PLATFORM</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>About FundedWhales</h2>
                <p>
                    FundedWhales is a comprehensive prop trading solution that provides traders with account management,
                    order processing, and integration with trading platforms. The application offers a secure
                    authentication system, order management, and MyFXBook API integration for tracking trading performance.
                </p>
            </div>

            <div class="section">
                <h2>Features</h2>
                <ul>
                    <li><span class="feature">User Authentication:</span> Secure login and registration system with email verification</li>
                    <li><span class="feature">Order Management:</span> Process and track trading orders with different account sizes and challenge types</li>
                    <li><span class="feature">MyFXBook Integration:</span> Connect and fetch data from MyFXBook accounts</li>
                    <li><span class="feature">Cloud Storage:</span> Image uploads for transaction verification using Cloudinary</li>
                    <li><span class="feature">Database Integration:</span> PostgreSQL database for storing user and order information</li>
                    <li><span class="feature">Email Notifications:</span> Automated email notifications with customized templates</li>
                    <li><span class="feature">Secure API:</span> JWT-based authentication for all API endpoints</li>
                    <li><span class="feature">Trading Account Management:</span> Automated account creation and management</li>
                </ul>
            </div>

            <div class="section">
                <h2>API Documentation</h2>
                <p>
                    FundedWhales provides a comprehensive REST API built with FastAPI. You can explore the full API documentation
                    using the interactive Swagger UI.
                </p>
                <div class="api-section">
                    <p><strong>API Documentation URL:</strong> <span class="endpoint">/docs</span></p>
                    <a href="/docs" class="button">Explore API Documentation</a>
                </div>

                <h3>Key Endpoints</h3>
                <ul>
                    <li><span class="endpoint">POST /auth/register</span> - Register new users</li>
                    <li><span class="endpoint">POST /auth/login</span> - User login</li>
                    <li><span class="endpoint">POST /order/order</span> - Create new trading orders</li>
                    <li><span class="endpoint">GET /order/orders</span> - Get user orders</li>
                    <li><span class="endpoint">POST /myfxbook/fetch_accounts</span> - Fetch MyFXBook accounts</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>© 2023 FundedWhales. All rights reserved.</p>
            <p>For support or inquiries, please contact <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

# Redirect root to landing page
@fastapi_app.get("/")
async def root():
    return {"message": "Welcome to FundedWhales Trading Platform API", "docs": "/docs", "landing_page": "/landing"}

# Export the FastAPI app for Hugging Face Spaces
app = fastapi_app

# Run the app if executed directly
if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run("app:app", host="0.0.0.0", port=port, reload=True)
