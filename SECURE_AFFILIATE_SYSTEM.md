# Secure Affiliate/Referral System

## Overview

This document describes the implementation of a secure affiliate/referral system where users can only access their referral codes after creating their first order and receiving admin approval.

## Key Security Features

1. **No Automatic Referral Code Generation**: Users no longer receive referral codes automatically upon registration
2. **Admin Approval Required**: Only admins can grant referral code access to users
3. **First Order Requirement**: Users must complete at least one order to be eligible for referral code access
4. **Access Tracking**: All referral code access grants are tracked with admin details and timestamps
5. **Conditional Display**: Referral codes are only shown to users who have been granted access

## Database Changes

### User Model Updates
- Added `is_admin: bool` field for admin role management
- Added `referral_code_access_enabled: bool` field to control referral code access
- Changed `referral_code` field to `Optional[str]` (no longer auto-generated)

### New Model: ReferralCodeAccess
Tracks when and by which admin referral code access was granted:
- `user_id`: The user who received access
- `granted_by_admin_id`: The admin who granted access
- `order_id`: The first order that qualified the user
- `granted_at`: Timestamp of when access was granted
- `notes`: Optional admin notes
- `is_revoked`: Flag for revoked access
- `revoked_at`: Timestamp of revocation
- `revoked_by_admin_id`: Admin who revoked access

## API Endpoints

### Admin Endpoints

#### 1. Get First-Time Order Users
```
GET /admin/first-order-users
```
**Description**: Get users who have created their first order and are eligible for referral code access.

**Query Parameters**:
- `limit` (int, default: 50, max: 100): Number of results to return
- `offset` (int, default: 0): Pagination offset
- `include_already_granted` (bool, default: false): Include users who already have access

**Response**:
```json
[
  {
    "user_id": 123,
    "username": "john_doe",
    "email": "<EMAIL>",
    "name": "John Doe",
    "order_id": 456,
    "order_created_at": "2025-01-31T10:30:00Z",
    "account_size": "$10,000",
    "challenge_type": "Phase 1",
    "payment_method": "Credit Card",
    "referral_code_access_enabled": false,
    "has_referral_code": false
  }
]
```

#### 2. Grant Referral Code Access
```
POST /admin/grant-referral-access
```
**Description**: Grant referral code access to a user who has completed their first order.

**Request Body**:
```json
{
  "user_id": 123,
  "order_id": 456,
  "notes": "User completed first $10k challenge successfully"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Referral code access granted successfully to john_doe",
  "user_id": 123,
  "referral_code": "ABC12345"
}
```

#### 3. Get Referral Access History
```
GET /admin/referral-access-history
```
**Description**: Get history of referral code access grants.

**Query Parameters**:
- `limit` (int, default: 50, max: 100): Number of results to return
- `offset` (int, default: 0): Pagination offset

**Response**:
```json
{
  "history": [
    {
      "access_id": 1,
      "user_id": 123,
      "username": "john_doe",
      "email": "<EMAIL>",
      "order_id": 456,
      "account_size": "$10,000",
      "granted_at": "2025-01-31T11:00:00Z",
      "granted_by_admin": "admin_user",
      "notes": "User completed first challenge successfully",
      "is_revoked": false,
      "revoked_at": null
    }
  ],
  "total_count": 1,
  "offset": 0,
  "limit": 50
}
```

### User Endpoints (Updated)

#### 1. Get User Profile
```
GET /auth/user/me
```
**Response** (referral_code only shown if access enabled):
```json
{
  "id": 123,
  "username": "john_doe",
  "email": "<EMAIL>",
  "name": "John Doe",
  "country": "USA",
  "phone_no": "+**********",
  "address": "123 Main St",
  "referral_code": "ABC12345",  // Only if referral_code_access_enabled = true
  "referral_code_access_enabled": true,
  "points_balance": 100,
  "is_verified": true
}
```

#### 2. Get Affiliate Stats
```
GET /affiliate/stats
```
**Error Response** (if access not enabled):
```json
{
  "detail": "Referral code access not enabled. Please complete your first order and wait for admin approval."
}
```

## Admin Authentication

### Creating Admin Users
To create an admin user, you need to manually update the database:

```sql
UPDATE users SET is_admin = true WHERE email = '<EMAIL>';
```

### Admin Endpoints Security
All admin endpoints use the `get_current_admin` dependency which:
1. Validates the user is authenticated
2. Checks that `is_admin = true`
3. Returns 403 Forbidden if user is not an admin

## Workflow

### For New Users
1. User registers → No referral code generated
2. User creates first order → Becomes eligible for referral code access
3. Admin reviews first-time order users via `/admin/first-order-users`
4. Admin grants access via `/admin/grant-referral-access`
5. System generates referral code and enables access
6. User can now see their referral code and use affiliate features

### For Existing Users (Migration)
Existing users who already have referral codes will need:
1. Set `referral_code_access_enabled = true` for users who should keep access
2. Set `referral_code_access_enabled = false` for users who need admin approval
3. Optionally create `ReferralCodeAccess` records for tracking

## Security Benefits

1. **Prevents Spam**: Users can't immediately start referring others without proving commitment
2. **Quality Control**: Admins can verify users are legitimate before granting referral privileges
3. **Audit Trail**: Complete tracking of who granted access and when
4. **Flexible Control**: Admins can revoke access if needed
5. **Order Verification**: Ensures users have actually made a purchase before getting referral benefits

## Migration Script

For existing installations, run this SQL to update existing users:

```sql
-- Add new columns to users table
ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN referral_code_access_enabled BOOLEAN DEFAULT FALSE;

-- Set existing users with referral codes to have access enabled (optional)
UPDATE users SET referral_code_access_enabled = TRUE WHERE referral_code IS NOT NULL;

-- Create admin user (replace with actual admin email)
UPDATE users SET is_admin = TRUE WHERE email = '<EMAIL>';
```

## Testing

1. Create a new user account
2. Verify no referral code is shown in profile
3. Create an order with that user
4. Login as admin and check `/admin/first-order-users`
5. Grant referral access via `/admin/grant-referral-access`
6. Verify user can now see referral code and access affiliate features
