"""
Simple test to check if giveaway system is working
"""

print("Starting simple giveaway test...")

try:
    # Test imports
    print("1. Testing imports...")
    from sqlmodel import Session
    from db import engine
    print("   ✅ Database imports successful")
    
    from models.giveaway import Giveaway, GiveawayEntry
    print("   ✅ Giveaway model imports successful")
    
    from routes.giveaway import auto_enter_giveaway
    print("   ✅ Giveaway function import successful")
    
    # Test database connection
    print("2. Testing database connection...")
    with Session(engine) as session:
        giveaways = session.query(Giveaway).all()
        print(f"   ✅ Found {len(giveaways)} giveaways in database")
        
        for giveaway in giveaways:
            print(f"      - {giveaway.title} (Status: {giveaway.status})")
    
    print("3. All tests passed! The giveaway system should be working.")
    print("   If emails are not being sent, the issue might be:")
    print("   - Email server configuration")
    print("   - The order creation is not reaching the giveaway code")
    print("   - Account size parsing issues")
    
except Exception as e:
    print(f"❌ Error: {str(e)}")
    import traceback
    traceback.print_exc()
