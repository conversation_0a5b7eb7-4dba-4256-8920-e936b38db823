#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create or designate an admin user for the secure affiliate system.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlmodel import Session
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_database_url():
    """Get database URL from environment"""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not found")
        sys.exit(1)
    
    # Convert postgres:// to postgresql+psycopg2:// if needed
    if database_url.startswith("postgres://"):
        database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)
    
    return database_url

def main():
    """Main function to create admin user"""
    print("🔧 Admin User Setup")
    print("=" * 30)
    
    admin_email = input("Enter admin email: ").strip()
    
    if not admin_email:
        print("❌ Email cannot be empty")
        sys.exit(1)
    
    try:
        # Get database connection
        database_url = get_database_url()
        engine = create_engine(database_url, echo=False)
        
        with Session(engine) as session:
            print("✅ Connected to database")
            
            # Check if user exists
            result = session.exec(text(f"""
                SELECT id, username, is_admin FROM "user" WHERE email = '{admin_email}'
            """))
            
            user = result.fetchone()
            
            if user:
                user_id, username, is_admin = user
                print(f"👤 Found user: {username} (ID: {user_id})")
                
                if is_admin:
                    print("✅ User is already an admin")
                else:
                    # Make user admin
                    session.exec(text(f"""
                        UPDATE "user" SET is_admin = TRUE WHERE email = '{admin_email}'
                    """))
                    session.commit()
                    print(f"✅ Granted admin privileges to {username}")
            else:
                print(f"❌ User with email {admin_email} not found")
                print("   Please create the user account first through normal registration")
                sys.exit(1)
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    print("\n🎉 Admin setup completed!")
    print(f"   User {admin_email} now has admin privileges")
    print("   They can now access admin endpoints like:")
    print("   - GET /admin/first-order-users")
    print("   - POST /admin/grant-referral-access")
    print("   - GET /admin/referral-access-history")

if __name__ == "__main__":
    main()
