from sqlmodel import SQLModel, Session, create_engine
from sqlalchemy.pool import <PERSON>ull<PERSON><PERSON>
import os
import time
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

# Ensure correct PostgreSQL format
database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)

# Fix SSL connection issues for Neon database
if "sslmode=" not in database_url:
    separator = "&" if "?" in database_url else "?"
    database_url += f"{separator}sslmode=require"

def create_engine_with_ssl_fix(database_url, max_retries=3):
    """Create database engine with SSL connection fixes for Neon/Heroku"""

    # SSL connection arguments for Neon database
    connect_args = {
        "sslmode": "require",
        "sslcert": None,
        "sslkey": None,
        "sslrootcert": None,
        "connect_timeout": 30,
        "application_name": "FundedWhales_Heroku"
    }

    for attempt in range(max_retries):
        try:
            print(f"🔄 Creating database engine (attempt {attempt + 1}/{max_retries})")

            engine = create_engine(
                database_url,
                echo=False,  # Disable verbose logging in production
                poolclass=NullPool,  # Disable connection pooling to prevent SSL issues
                pool_pre_ping=True,  # Test connections before use
                pool_recycle=300,    # Recycle connections every 5 minutes
                connect_args=connect_args
            )

            # Test the connection
            with engine.connect() as conn:
                result = conn.execute("SELECT 1")
                result.fetchone()

            print("✅ Database engine created successfully with SSL fix")
            return engine

        except Exception as e:
            print(f"❌ Engine creation attempt {attempt + 1} failed: {e}")

            if "SSL connection has been closed unexpectedly" in str(e):
                print("🔐 SSL connection issue detected - applying fixes...")
                # Modify SSL settings for next attempt
                connect_args["sslmode"] = "prefer"

            if attempt < max_retries - 1:
                print(f"⏳ Retrying in 2 seconds...")
                time.sleep(2)
            else:
                print("❌ All engine creation attempts failed")
                # Return a basic engine as fallback
                return create_engine(
                    database_url,
                    echo=False,
                    poolclass=NullPool,
                    connect_args={"sslmode": "prefer"}
                )

# Create the engine with SSL fixes
engine = create_engine_with_ssl_fix(database_url)

def create_db_and_tables():
    """Create database tables based on SQLModel definitions."""
    # Import all models to ensure they're registered with SQLModel metadata
    # This import is used to register the model with SQLModel metadata
    # pylint: disable=unused-import
    from routes.account import AccountCredential
    from models.user import User
    from models.points_transaction import PointsTransaction
    from models.user_verification import UserVerification
    from models.referral_access import ReferralCodeAccess
    from models.order import OrderModel, OrderImage, DateTimeModel, PassOrder, OrderTimeline, CompleteOrderModel, Stage2Account, LiveAccount, RejectOrder, FailOrder, Certificate
    from models.giveaway import Giveaway, GiveawayEntry, GiveawayWinner
    from models.affiliate import AffiliateSettings, AffiliateUser, AffiliateCommission, AffiliatePayment, AffiliateLink

    # Create all tables
    SQLModel.metadata.create_all(engine)

    # Explicitly create the account_credentials table
    try:
        AccountCredential.create_table(engine)
        print("Ensured account_credentials table exists")
    except Exception as e:
        print(f"Error ensuring account_credentials table: {str(e)}")

    # Add created_at column to ordermodel table if it doesn't exist
    try:
        with Session(engine) as session:
            # Check if the column exists
            from sqlalchemy import text
            result = session.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name='ordermodel' AND column_name='created_at';
            """))

            if not result.fetchone():
                print("Adding created_at column to ordermodel table...")
                session.execute(text("""
                    ALTER TABLE ordermodel
                    ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                """))
                session.commit()
                print("created_at column added successfully.")
            else:
                print("created_at column already exists in ordermodel table.")
    except Exception as e:
        print(f"Error checking/adding created_at column: {str(e)}")

def get_session():
    """Provide a database session using a context manager."""
    with Session(engine) as session:
        yield session
