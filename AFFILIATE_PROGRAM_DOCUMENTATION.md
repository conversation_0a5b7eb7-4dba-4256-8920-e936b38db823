# 💰 Affiliate Program Documentation

## 🎯 **Overview**

The affiliate program allows users to earn points on every order placed by users they referred. It features a tiered commission system, automatic point distribution, and comprehensive tracking.

## 🏗️ **System Architecture**

### **Database Models**
- `AffiliateSettings` - Global program configuration
- `AffiliateUser` - Individual affiliate statistics and settings
- `AffiliateCommission` - Individual commission records
- `AffiliatePayment` - Payment/point distribution tracking
- `AffiliateLink` - Custom affiliate links (future feature)

### **Commission Tiers**
- 🥉 **Bronze** (0+ orders from referrals): 25 points per order
- 🥈 **Silver** (10+ orders from referrals): 35 points per order  
- 🥇 **Gold** (25+ orders from referrals): 50 points per order

## 🔄 **How It Works**

### **1. User Registration**
- Users automatically become affiliates when they register
- Each user gets a unique referral code
- No additional signup required for affiliate program

### **2. Referral Process**
- User A shares their referral code with User B
- User B registers using User A's referral code
- User A becomes User B's "referrer"

### **3. Commission Earning**
- When User B places ANY order (any amount), User A earns commission
- Commission is automatically calculated based on User A's tier
- Points are immediately added to User A's balance
- User A receives an email notification

### **4. Tier Progression**
- Tiers are based on total orders placed by ALL referred users
- Automatic tier upgrades when thresholds are reached
- Higher tiers earn more points per order

## 📊 **API Endpoints**

### **User Endpoints**
```
GET /affiliate/stats
- Get current user's affiliate statistics
- Returns: tier, total referrals, commission earned, etc.

GET /affiliate/commissions?limit=50&offset=0
- Get current user's commission history
- Returns: list of commission records with details

GET /affiliate/settings
- Get current affiliate program settings
- Returns: commission rates, tier thresholds, program status
```

### **Admin Endpoints**
```
POST /affiliate/admin/settings
- Update affiliate program settings
- Body: commission rates, tier settings, program status

GET /affiliate/admin/users?limit=100&offset=0
- Get all affiliate users with statistics
- Returns: list of all affiliates and their performance

POST /affiliate/admin/user/{user_id}/commission
- Manually award commission points to a user
- Body: points amount, description
```

## 🎨 **Email Templates**

### **Commission Earned Email**
- Sent when user earns commission from referred user's order
- Shows commission amount, order details, tier status
- Includes performance statistics and tier progress
- Preview: `http://localhost:8000/email-preview/affiliate-commission`

### **Tier Upgrade Email**
- Sent when user gets upgraded to higher tier
- Celebrates achievement and shows new benefits
- Explains increased commission rates
- Preview: `http://localhost:8000/email-preview/affiliate-tier-upgrade`

## 🔧 **Configuration**

### **Default Settings**
```python
commission_type = "tiered"           # Use tiered commission system
program_active = True                # Program is active
min_order_value = 0.0               # No minimum order value
min_referrals_required = 1          # Need at least 1 referral

# Tier Configuration
tier1_orders = 0                    # Bronze: 0+ orders
tier1_points = 25                   # Bronze: 25 points per order
tier2_orders = 10                   # Silver: 10+ orders  
tier2_points = 35                   # Silver: 35 points per order
tier3_orders = 25                   # Gold: 25+ orders
tier3_points = 50                   # Gold: 50 points per order
```

### **Customization Options**
- Commission type: Fixed points, percentage, or tiered
- Minimum order value for commission eligibility
- Tier thresholds and commission rates
- Individual user custom rates (override global settings)

## 🚀 **Setup Instructions**

### **1. Initialize Database**
```bash
# Run this to create affiliate tables and default settings
python create_affiliate_settings.py
```

### **2. Verify Integration**
- Affiliate system is automatically integrated with order creation
- When any user places an order, system checks for referrer
- If referrer exists, commission is automatically processed

### **3. Test the System**
```bash
# Run comprehensive test suite
python test_affiliate_system.py
```

## 📈 **Integration Points**

### **Order System Integration**
- Automatically processes commissions when orders are created
- Parses order value from account size
- Awards points based on current tier
- Sends notification emails

### **Points System Integration**
- Commissions are added as points transactions
- Shows up in user's points balance
- Tracked separately as "referral points earned"
- Integrated with existing points/balance endpoint

### **Email System Integration**
- Automatic commission notification emails
- Tier upgrade celebration emails
- Uses existing email infrastructure

## 🧪 **Testing**

### **Manual Testing Flow**
1. Register User A (gets referral code)
2. Register User B with User A's referral code
3. User B places an order (any amount)
4. Verify User A receives commission points
5. Check User A receives commission email
6. Verify commission appears in User A's history

### **API Testing**
```bash
# Get affiliate stats
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:8000/affiliate/stats

# Get commission history  
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:8000/affiliate/commissions

# Get program settings
curl http://localhost:8000/affiliate/settings
```

## 📊 **Monitoring & Analytics**

### **Key Metrics to Track**
- Total active affiliates
- Commission points distributed
- Average commissions per affiliate
- Tier distribution (Bronze/Silver/Gold)
- Conversion rates (referrals to orders)

### **Available Data**
- Individual affiliate performance
- Commission history with order details
- Tier progression tracking
- Monthly commission totals

## 🔒 **Security & Validation**

### **Commission Validation**
- Verifies user was actually referred
- Checks affiliate program is active
- Validates minimum order requirements
- Prevents duplicate commissions for same order

### **Fraud Prevention**
- One commission per order
- Referrer must exist and be active
- Order must be valid and processed
- Commission records are immutable

## 🎯 **Future Enhancements**

### **Planned Features**
- Custom affiliate links with tracking
- Performance-based bonus multipliers
- Affiliate leaderboards and competitions
- Advanced analytics dashboard
- Payout management system

### **Potential Improvements**
- Recurring commission for subscription orders
- Team/sub-affiliate structures
- Geographic performance tracking
- A/B testing for commission rates

The affiliate program is now fully functional and integrated with your existing order and points systems! 🚀
