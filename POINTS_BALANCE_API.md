# Enhanced Points Balance API

## 📊 **Endpoint: GET /points/balance**

The points balance endpoint has been enhanced to provide comprehensive information about the user's points, referral activity, and referred users.

### **🔐 Authentication**
- **Required**: Bearer token authentication
- **Header**: `Authorization: Bearer <token>`

### **📋 Response Format**

```json
{
  "user_id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "points_balance": 150,
  "referral_code": "ABC12345",
  "total_referrals": 3,
  "total_referral_points_earned": 150,
  "referred_users": [
    {
      "id": 2,
      "username": "jane_smith",
      "email": "<EMAIL>",
      "created_at": "2025-01-15T10:30:00Z"
    },
    {
      "id": 3,
      "username": "bob_wilson",
      "email": "<EMAIL>",
      "created_at": "2025-01-16T14:20:00Z"
    }
  ]
}
```

### **📝 Response Fields**

| Field | Type | Description |
|-------|------|-------------|
| `user_id` | integer | Current user's unique ID |
| `username` | string | Current user's username |
| `email` | string | Current user's email address |
| `points_balance` | integer | Current points balance |
| `referral_code` | string | User's unique referral code |
| `total_referrals` | integer | Number of users who registered with this referral code |
| `total_referral_points_earned` | integer | Total points earned from referrals |
| `referred_users` | array | List of users who used this referral code |

### **👥 Referred Users Object**

Each user in the `referred_users` array contains:

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Referred user's unique ID |
| `username` | string | Referred user's username |
| `email` | string | Referred user's email address |
| `created_at` | datetime | When the user registered |

### **🚀 Usage Examples**

#### **JavaScript/Fetch**
```javascript
fetch('/points/balance', {
    headers: {
        'Authorization': 'Bearer ' + token
    }
})
.then(response => response.json())
.then(data => {
    console.log('Points Balance:', data.points_balance);
    console.log('Referral Code:', data.referral_code);
    console.log('Total Referrals:', data.total_referrals);
    
    // Display referred users
    data.referred_users.forEach(user => {
        console.log(`Referred: ${user.username} (${user.email})`);
    });
});
```

#### **Python/Requests**
```python
import requests

headers = {'Authorization': f'Bearer {token}'}
response = requests.get('http://localhost:8000/points/balance', headers=headers)
data = response.json()

print(f"Points: {data['points_balance']}")
print(f"Referral Code: {data['referral_code']}")
print(f"Total Referrals: {data['total_referrals']}")
print(f"Referral Points Earned: {data['total_referral_points_earned']}")

print("\nReferred Users:")
for user in data['referred_users']:
    print(f"  - {user['username']} ({user['email']}) - Joined: {user['created_at']}")
```

#### **cURL**
```bash
curl -X GET "http://localhost:8000/points/balance" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### **🎯 Use Cases**

1. **Dashboard Display**: Show user's points and referral statistics
2. **Referral Tracking**: Display list of successfully referred users
3. **Earnings Summary**: Show total points earned from referrals
4. **User Management**: Access referred users' information for support

### **📊 Frontend Integration Examples**

#### **React Component**
```jsx
import React, { useState, useEffect } from 'react';

function PointsBalance({ token }) {
    const [balance, setBalance] = useState(null);
    
    useEffect(() => {
        fetch('/points/balance', {
            headers: { 'Authorization': `Bearer ${token}` }
        })
        .then(res => res.json())
        .then(setBalance);
    }, [token]);
    
    if (!balance) return <div>Loading...</div>;
    
    return (
        <div className="points-balance">
            <h2>Points Balance: {balance.points_balance}</h2>
            <p>Referral Code: <strong>{balance.referral_code}</strong></p>
            <p>Total Referrals: {balance.total_referrals}</p>
            <p>Referral Points Earned: {balance.total_referral_points_earned}</p>
            
            <h3>Referred Users:</h3>
            {balance.referred_users.length > 0 ? (
                <ul>
                    {balance.referred_users.map(user => (
                        <li key={user.id}>
                            {user.username} ({user.email}) - {new Date(user.created_at).toLocaleDateString()}
                        </li>
                    ))}
                </ul>
            ) : (
                <p>No referrals yet. Share your referral code to earn points!</p>
            )}
        </div>
    );
}
```

#### **Vue.js Component**
```vue
<template>
  <div class="points-balance">
    <h2>Points Balance: {{ balance?.points_balance }}</h2>
    <p>Referral Code: <strong>{{ balance?.referral_code }}</strong></p>
    <p>Total Referrals: {{ balance?.total_referrals }}</p>
    <p>Referral Points Earned: {{ balance?.total_referral_points_earned }}</p>
    
    <h3>Referred Users:</h3>
    <ul v-if="balance?.referred_users?.length">
      <li v-for="user in balance.referred_users" :key="user.id">
        {{ user.username }} ({{ user.email }}) - {{ formatDate(user.created_at) }}
      </li>
    </ul>
    <p v-else>No referrals yet. Share your referral code to earn points!</p>
  </div>
</template>

<script>
export default {
  props: ['token'],
  data() {
    return { balance: null };
  },
  async mounted() {
    const response = await fetch('/points/balance', {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    this.balance = await response.json();
  },
  methods: {
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    }
  }
};
</script>
```

### **🔒 Security Notes**

- Endpoint requires valid authentication
- Users can only see their own referral information
- Email addresses of referred users are included (consider privacy implications)
- No sensitive data like passwords or payment info is exposed

### **⚡ Performance**

- Single database query for referred users
- Separate query for referral points calculation
- Response is cached-friendly (user-specific data)
- Efficient for dashboard displays

### **🎁 Integration with Referral System**

This endpoint works seamlessly with the referral system:
- Shows real-time referral count
- Displays actual referred users with their details
- Calculates total points earned from referrals
- Updates automatically when new users register with the referral code

The enhanced endpoint provides everything needed for a comprehensive referral dashboard! 🚀
