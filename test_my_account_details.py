#!/usr/bin/env python3
"""
Test script for the new /order/my-account-details endpoint.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"  # Adjust if your app runs on different port

def test_user_login():
    """Test user login and get token"""
    print("🧪 Testing user login...")
    
    # Try to login with existing user
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data["access_token"]
            print("   ✅ Login successful")
            return token
        else:
            print(f"   ❌ Login failed: {response.json()}")
            return None
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return None

def test_my_account_details(token):
    """Test the new my-account-details endpoint"""
    print("🧪 Testing /order/my-account-details endpoint...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/order/my-account-details", headers=headers)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ SUCCESS - Account details retrieved")
            
            # Display user info
            user_info = data.get("user", {})
            print(f"   👤 User: {user_info.get('username')} ({user_info.get('email')})")
            print(f"   📊 Total Orders: {data.get('total_orders', 0)}")
            
            # Display order details
            orders = data.get("orders", [])
            for i, order in enumerate(orders, 1):
                print(f"\n   📋 Order {i}:")
                print(f"      Order ID: {order.get('order_id')}")
                print(f"      Status: {order.get('status')}")
                print(f"      Account Size: {order.get('account_size')}")
                print(f"      Challenge Type: {order.get('challenge_type')}")
                print(f"      Created At: {order.get('created_at')}")
                print(f"      Platform: {order.get('platform')}")
                
                # Show status-specific details
                status = order.get('status')
                
                if status == "Completed":
                    print(f"      Completed At: {order.get('completed_at')}")
                    credentials = order.get('credentials', {})
                    if credentials.get('server'):
                        print(f"      Server: {credentials.get('server')}")
                        print(f"      Login: {credentials.get('platform_login')}")
                
                elif status == "Passed":
                    print(f"      Passed At: {order.get('passed_at')}")
                    print(f"      Profit Amount: {order.get('profit_amount')}")
                    pass_creds = order.get('pass_credentials', {})
                    if pass_creds.get('server'):
                        print(f"      Pass Server: {pass_creds.get('server')}")
                        print(f"      Pass Login: {pass_creds.get('platform_login')}")
                
                elif status == "Stage 2 Account":
                    print(f"      Stage 2 Created At: {order.get('stage2_created_at')}")
                    stage2_creds = order.get('stage2_credentials', {})
                    if stage2_creds.get('server'):
                        print(f"      Stage 2 Server: {stage2_creds.get('server')}")
                        print(f"      Stage 2 Login: {stage2_creds.get('platform_login')}")
                        print(f"      Profit Target: {stage2_creds.get('profit_target')}")
                
                elif status == "Live Account":
                    print(f"      Live Created At: {order.get('live_created_at')}")
                    live_creds = order.get('live_credentials', {})
                    if live_creds.get('server'):
                        print(f"      Live Server: {live_creds.get('server')}")
                        print(f"      Live Login: {live_creds.get('platform_login')}")
                        print(f"      Profit Target: {live_creds.get('profit_target')}")
                
                elif status == "Failed":
                    print(f"      Failed At: {order.get('failed_at')}")
                    print(f"      Fail Reason: {order.get('fail_reason')}")
                    print(f"      Fail Notes: {order.get('fail_notes')}")
            
            return True
        else:
            print(f"   ❌ FAILED - {response.json()}")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing My Account Details Endpoint")
    print("=" * 50)
    
    # Test 1: Login
    token = test_user_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        sys.exit(1)
    
    # Test 2: My account details
    success = test_my_account_details(token)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 My Account Details Test Completed Successfully!")
        print("\n📋 Endpoint Features:")
        print("   ✅ Shows user information")
        print("   ✅ Lists all user orders")
        print("   ✅ Shows basic order info for all statuses")
        print("   ✅ Shows credentials for completed orders")
        print("   ✅ Shows pass details and credentials for passed orders")
        print("   ✅ Shows stage 2 credentials and profit targets")
        print("   ✅ Shows live account credentials and profit targets")
        print("   ✅ Shows failure details for failed orders")
    else:
        print("❌ My Account Details Test Failed!")
    
    print("\n🔗 Endpoint URL: GET /order/my-account-details")
    print("   Requires: Authorization Bearer token")
    print("   Returns: Complete user account details with status-specific information")

if __name__ == "__main__":
    main()
