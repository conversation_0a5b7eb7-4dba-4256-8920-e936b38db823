"""
<PERSON><PERSON><PERSON> to create a default giveaway for testing the giveaway system
Run this script to initialize a default giveaway in your database
"""

from datetime import datetime, timedelta
from sqlmodel import Session
from db import engine
from models.giveaway import Giveaway, GiveawayStatus

def create_default_giveaway():
    """Create a default giveaway for testing"""
    
    # Calculate dates
    start_date = datetime.utcnow()
    end_date = start_date + timedelta(days=30)  # 30 days from now
    
    with Session(engine) as session:
        # Check if a giveaway already exists
        existing_giveaway = session.query(Giveaway).first()
        if existing_giveaway:
            print(f"Giveaway already exists: {existing_giveaway.title}")
            return existing_giveaway
        
        # Create new giveaway
        giveaway = Giveaway(
            title="Monthly $100K Account Giveaway",
            description="Win a fully funded $100,000 trading account! Automatically enter by purchasing any account of $50,000 or more. The more qualifying purchases you make, the more chances you have to win!",
            prize_description="$100,000 Funded Trading Account",
            prize_value=100000.0,
            min_account_size=50000.0,
            start_date=start_date,
            end_date=end_date,
            status=GiveawayStatus.ACTIVE,
            max_entries=None,  # No limit on entries
            terms_and_conditions="""
            Terms and Conditions:
            1. Must purchase an account of $50,000 or more to qualify
            2. Each qualifying purchase gives you one entry
            3. Multiple entries allowed (one per qualifying purchase)
            4. Winner will be randomly selected on the end date
            5. Prize must be claimed within 30 days of notification
            6. FundedWhales reserves the right to verify eligibility
            7. Employees and affiliates of FundedWhales are not eligible
            8. Winner will be notified via email and must respond within 7 days
            9. Prize has no cash value and cannot be transferred
            10. By participating, you agree to these terms and conditions
            """
        )
        
        session.add(giveaway)
        session.commit()
        session.refresh(giveaway)
        
        print(f"✅ Created default giveaway: {giveaway.title}")
        print(f"   Prize: {giveaway.prize_description}")
        print(f"   Min Account Size: ${giveaway.min_account_size:,.0f}")
        print(f"   Start Date: {giveaway.start_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   End Date: {giveaway.end_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Status: {giveaway.status}")
        
        return giveaway

def main():
    """Main function to run the script"""
    print("🎁 Creating Default Giveaway...")
    print("=" * 50)
    
    try:
        giveaway = create_default_giveaway()
        print("\n✅ Default giveaway created successfully!")
        print(f"   Giveaway ID: {giveaway.id}")
        print("\n📧 Test the system by:")
        print("   1. Creating an order with account size $50,000+")
        print("   2. Check if user gets automatically entered")
        print("   3. Verify giveaway entry email is sent")
        print("\n🔗 Preview giveaway emails at:")
        print("   http://localhost:8000/email-preview/giveaway-entry")
        print("   http://localhost:8000/email-preview/giveaway-winner")
        
    except Exception as e:
        print(f"❌ Error creating default giveaway: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    main()
