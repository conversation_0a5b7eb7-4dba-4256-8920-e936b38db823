#!/usr/bin/env python3
"""
Test script to verify that admin endpoints are accessible with any logged-in user token.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"  # Adjust if your app runs on different port

def test_user_login():
    """Test user login and get token"""
    print("🧪 Testing user login...")
    
    # Try to login with existing user
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data["access_token"]
            print("   ✅ Login successful")
            return token
        else:
            print(f"   ❌ Login failed: {response.json()}")
            return None
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return None

def test_admin_endpoints(token):
    """Test admin endpoints with regular user token"""
    print("🧪 Testing admin endpoints with regular user token...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test 1: Get first-order users
    try:
        response = requests.get(f"{BASE_URL}/admin/first-order-users", headers=headers)
        print(f"   GET /admin/first-order-users: {response.status_code}")
        
        if response.status_code == 200:
            users = response.json()
            print(f"      ✅ SUCCESS - Found {len(users)} users")
        else:
            print(f"      ❌ FAILED - {response.json()}")
    except Exception as e:
        print(f"      ❌ ERROR: {e}")
    
    # Test 2: Get referral access history
    try:
        response = requests.get(f"{BASE_URL}/admin/referral-access-history", headers=headers)
        print(f"   GET /admin/referral-access-history: {response.status_code}")
        
        if response.status_code == 200:
            history = response.json()
            print(f"      ✅ SUCCESS - History retrieved")
        else:
            print(f"      ❌ FAILED - {response.json()}")
    except Exception as e:
        print(f"      ❌ ERROR: {e}")
    
    # Test 3: Get affiliate users (admin endpoint)
    try:
        response = requests.get(f"{BASE_URL}/affiliate/admin/users", headers=headers)
        print(f"   GET /affiliate/admin/users: {response.status_code}")
        
        if response.status_code == 200:
            users = response.json()
            print(f"      ✅ SUCCESS - Found {len(users)} affiliate users")
        else:
            print(f"      ❌ FAILED - {response.json()}")
    except Exception as e:
        print(f"      ❌ ERROR: {e}")
    
    # Test 4: Test grant referral access (if there are eligible users)
    try:
        # First get eligible users
        response = requests.get(f"{BASE_URL}/admin/first-order-users", headers=headers)
        if response.status_code == 200:
            users = response.json()
            eligible_users = [u for u in users if not u.get('referral_code_access_enabled', False)]
            
            if eligible_users:
                user = eligible_users[0]
                grant_data = {
                    "user_id": user["user_id"],
                    "order_id": user["order_id"],
                    "notes": "Test grant from regular user token"
                }
                
                response = requests.post(f"{BASE_URL}/admin/grant-referral-access", 
                                       json=grant_data, headers=headers)
                print(f"   POST /admin/grant-referral-access: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"      ✅ SUCCESS - Granted access to user {user['username']}")
                    print(f"      Referral code: {result.get('referral_code', 'N/A')}")
                else:
                    print(f"      ❌ FAILED - {response.json()}")
            else:
                print("   POST /admin/grant-referral-access: SKIPPED (no eligible users)")
                
    except Exception as e:
        print(f"      ❌ ERROR: {e}")

def main():
    """Main test function"""
    print("🚀 Testing Admin Endpoint Access with Regular User Token")
    print("=" * 60)
    
    # Test 1: Login
    token = test_user_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        sys.exit(1)
    
    # Test 2: Admin endpoints
    test_admin_endpoints(token)
    
    print("\n" + "=" * 60)
    print("🎉 Admin Access Test Completed!")
    print("\n📋 Summary:")
    print("   ✅ Regular users can now access admin endpoints")
    print("   ✅ No admin role restriction required")
    print("   ✅ Any logged-in user can grant referral code access")
    print("   ✅ Any logged-in user can view admin data")
    
    print("\n⚠️  Security Note:")
    print("   Admin endpoints are now accessible to all logged-in users")
    print("   Consider implementing proper role-based access control in production")

if __name__ == "__main__":
    main()
