<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxury Certificate Design 4</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Raleway:wght@300;400;600;700&family=Dancing+Script:wght@600&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Raleway', sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 10px;
        }
        
        .certificate-container {
            width: 100%;
            max-width: 1000px;
            position: relative;
            margin: 0 auto;
        }
        
        .certificate {
            background: linear-gradient(to right, #0a1f44, #0c2461, #0a1f44);
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            aspect-ratio: 1.8 / 1;
            overflow: hidden;
            color: white;
        }
        
        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: radial-gradient(circle, #fff 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        .border-top {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #e67e22;
        }
        
        .border-bottom {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #e67e22;
        }
        
        .border-left {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 2px;
            background-color: #e67e22;
        }
        
        .border-right {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 2px;
            background-color: #e67e22;
        }
        
        .inner-frame {
            position: absolute;
            top: 25px;
            left: 25px;
            right: 25px;
            bottom: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .content {
            position: relative;
            z-index: 4;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            height: 100%;
            padding: 40px 60px;
            overflow-y: auto;
        }
        
        .header-section {
            margin-bottom: 15px;
            width: 100%;
            position: relative;
        }
        
        .logo-container {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo {
            height: 50px;
            width: auto;
        }
        
        .company-name {
            color: #e67e22;
            font-size: 18px;
            letter-spacing: 2px;
            text-transform: uppercase;
            margin-bottom: 6px;
            font-weight: 600;
        }
        
        .certificate-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 2px;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
        
        .certificate-subtitle {
            font-size: 18px;
            font-weight: 300;
            letter-spacing: 1px;
            margin-bottom: 12px;
            opacity: 0.9;
        }
        
        .divider-container {
            display: flex;
            align-items: center;
            width: 220px;
            margin: 0 auto 15px;
        }
        
        .divider-line {
            flex: 1;
            height: 1px;
            background-color: #e67e22;
        }
        
        .divider-diamond {
            margin: 0 12px;
            width: 12px;
            height: 12px;
            background-color: #e67e22;
            transform: rotate(45deg);
        }
        
        .main-section {
            max-width: 700px;
            margin: 0 auto;
        }
        
        .recipient-label {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 8px;
        }
        
        .recipient-name {
            font-family: 'Playfair Display', serif;
            font-size: 26px;
            color: #e67e22;
            margin-bottom: 12px;
        }
        
        .certificate-text {
            font-size: 15px;
            opacity: 0.8;
            max-width: 600px;
            margin-bottom: 12px;
            line-height: 1.5;
        }
        
        .achievement-details {
            background-color: rgba(12, 36, 97, 0.5);
            border: 1px solid rgba(230, 126, 34, 0.3);
            padding: 10px 20px;
            border-radius: 5px;
            margin-bottom: 15px;
            max-width: 600px;
        }
        
        .achievement-details p {
            margin: 3px 0;
            font-size: 14px;
            opacity: 0.9;
            text-align: left;
        }
        
        .achievement-details strong {
            color: #e67e22;
            font-weight: 600;
        }
        
        .motivation-text {
            font-size: 14px;
            opacity: 0.9;
            max-width: 600px;
            margin-bottom: 15px;
            line-height: 1.5;
            font-style: italic;
            padding: 0 10px;
        }
        
        .motivation-text strong {
            color: #e67e22;
            font-weight: 600;
            font-style: normal;
        }
        
        .footer-section {
            width: 100%;
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .signatures {
            display: flex;
            justify-content: space-between;
            width: 100%;
            max-width: 550px;
        }
        
        .signature {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .signature-line {
            width: 130px;
            border-bottom: 1px solid rgba(230, 126, 34, 0.5);
            margin-bottom: 4px;
        }
        
        .signature-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .handwritten-signature {
            font-family: 'Dancing Script', cursive;
            font-size: 24px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 4px;
            transform: rotate(-5deg);
            position: relative;
            top: -4px;
        }
        
        .date-value {
            font-size: 16px;
            color: #e67e22;
            margin-bottom: 5px;
        }
        
        .certificate-id {
            margin-top: 12px;
            font-size: 11px;
            opacity: 0.7;
            letter-spacing: 1px;
        }
        
        /* Desktop optimization */
        @media (min-width: 1200px) {
            .certificate-container {
                max-width: 1100px;
            }
            
            .certificate {
                aspect-ratio: 2 / 1;
            }
            
            .content {
                padding: 40px 70px;
            }
            
            /* Desktop-specific logo positioning */
            .header-section {
                margin-bottom: 15px;
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
            }
            
            .logo-container {
                position: absolute;
                top: 15px; /* Move down a bit */
                left: 0;
                margin-bottom: 0;
            }
            
            .logo {
                height: 55px;
            }
            
            .company-name {
                font-size: 20px;
                margin-bottom: 6px;
            }
            
            .certificate-title {
                font-size: 34px;
            }
            
            .certificate-subtitle {
                font-size: 20px;
                margin-bottom: 12px;
            }
            
            .divider-container {
                width: 250px;
                margin-bottom: 18px;
            }
            
            .recipient-label {
                font-size: 18px;
            }
            
            .recipient-name {
                font-size: 28px;
            }
            
            .certificate-text, .motivation-text {
                font-size: 16px;
                line-height: 1.5;
            }
            
            .achievement-details {
                padding: 12px 25px;
            }
            
            .achievement-details p {
                font-size: 15px;
                margin: 4px 0;
            }
            
            .footer-section {
                margin-top: 18px;
            }
            
            .signatures {
                max-width: 600px;
            }
            
            .signature-line {
                width: 150px;
            }
            
            .handwritten-signature {
                font-size: 26px;
            }
            
            .date-value {
                font-size: 18px;
            }
            
            .certificate-id {
                margin-top: 15px;
                font-size: 12px;
            }
        }
        
        /* Extra large screens */
        @media (min-width: 1600px) {
            .certificate {
                aspect-ratio: 2.2 / 1;
            }
            
            .content {
                padding: 45px 80px;
            }
            
            .logo {
                height: 60px;
            }
            
            .logo-container {
                top: 20px; /* Move down a bit more on very large screens */
            }
        }
        
        /* Mobile-friendly styles */
        @media print {
            body {
                background: none;
                padding: 0;
            }
            
            .certificate-container {
                max-width: none;
                width: 100%;
                height: 100%;
            }
            
            .certificate {
                box-shadow: none;
                aspect-ratio: 1.414 / 1;
            }
        }
        
        /* Large phones and small tablets */
        @media (max-width: 768px) {
            .certificate {
                aspect-ratio: 1.414 / 1;
            }
            
            .content {
                padding: 40px 20px;
            }
            
            .inner-frame {
                top: 20px;
                left: 20px;
                right: 20px;
                bottom: 20px;
            }
            
            /* Mobile-specific logo positioning (centered) */
            .header-section {
                margin-bottom: 25px;
                display: block;
            }
            
            .logo-container {
                position: static;
                margin-bottom: 8px;
                display: flex;
                justify-content: center;
            }
            
            .logo {
                height: 50px;
            }
            
            .company-name {
                font-size: 18px;
                margin-bottom: 8px;
            }
            
            .certificate-title {
                font-size: 28px;
            }
            
            .certificate-subtitle {
                font-size: 18px;
                margin-bottom: 15px;
            }
            
            .divider-container {
                width: 200px;
                margin-bottom: 25px;
            }
            
            .recipient-label {
                font-size: 16px;
                margin-bottom: 12px;
            }
            
            .recipient-name {
                font-size: 24px;
                margin-bottom: 15px;
            }
            
            .certificate-text {
                font-size: 15px;
                margin-bottom: 15px;
            }
            
            .achievement-details {
                padding: 12px 20px;
                margin-bottom: 20px;
            }
            
            .achievement-details p {
                font-size: 14px;
                margin: 4px 0;
            }
            
            .motivation-text {
                font-size: 15px;
                margin-bottom: 20px;
                padding: 0 10px;
            }
            
            .footer-section {
                margin-top: 25px;
            }
            
            .signatures {
                max-width: 100%;
            }
            
            .signature-line {
                width: 120px;
            }
            
            .handwritten-signature {
                font-size: 24px;
            }
            
            .date-value {
                font-size: 16px;
            }
            
            .certificate-id {
                margin-top: 15px;
                font-size: 11px;
            }
        }
        
        /* Small phones */
        @media (max-width: 480px) {
            .certificate {
                aspect-ratio: auto;
                min-height: 100vh;
            }
            
            .content {
                padding: 30px 15px;
            }
            
            .inner-frame {
                top: 15px;
                left: 15px;
                right: 15px;
                bottom: 15px;
            }
            
            .header-section {
                margin-bottom: 20px;
            }
            
            .logo {
                height: 40px;
            }
            
            .company-name {
                font-size: 16px;
                letter-spacing: 2px;
                margin-bottom: 6px;
            }
            
            .certificate-title {
                font-size: 24px;
                letter-spacing: 2px;
            }
            
            .certificate-subtitle {
                font-size: 16px;
                letter-spacing: 1px;
                margin-bottom: 12px;
            }
            
            .divider-container {
                width: 180px;
                margin-bottom: 20px;
            }
            
            .divider-diamond {
                width: 12px;
                height: 12px;
                margin: 0 10px;
            }
            
            .recipient-label {
                font-size: 14px;
                margin-bottom: 10px;
            }
            
            .recipient-name {
                font-size: 22px;
                margin-bottom: 12px;
            }
            
            .certificate-text {
                font-size: 14px;
                margin-bottom: 12px;
                line-height: 1.5;
            }
            
            .achievement-details {
                padding: 10px 15px;
                margin-bottom: 15px;
            }
            
            .achievement-details p {
                font-size: 13px;
                margin: 3px 0;
            }
            
            .motivation-text {
                font-size: 14px;
                margin-bottom: 15px;
                line-height: 1.5;
                padding: 0 5px;
            }
            
            .footer-section {
                margin-top: 20px;
            }
            
            .signatures {
                flex-direction: column;
                gap: 20px;
                align-items: center;
            }
            
            .signature-line {
                width: 100px;
            }
            
            .signature-label {
                font-size: 12px;
            }
            
            .handwritten-signature {
                font-size: 22px;
            }
            
            .date-value {
                font-size: 14px;
            }
            
            .certificate-id {
                margin-top: 15px;
                font-size: 10px;
            }
        }
        
        /* Extra small phones */
        @media (max-width: 360px) {
            .content {
                padding: 25px 12px;
            }
            
            .logo {
                height: 35px;
            }
            
            .company-name {
                font-size: 14px;
            }
            
            .certificate-title {
                font-size: 22px;
            }
            
            .certificate-subtitle {
                font-size: 14px;
            }
            
            .recipient-name {
                font-size: 20px;
            }
            
            .achievement-details p {
                font-size: 12px;
            }
            
            .motivation-text {
                font-size: 13px;
            }
            
            .date-value {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <div class="certificate">
            <div class="background-pattern"></div>
            <div class="border-top"></div>
            <div class="border-bottom"></div>
            <div class="border-left"></div>
            <div class="border-right"></div>
            <div class="inner-frame"></div>
            
            <div class="content">
                <div class="header-section">
                    <div class="logo-container">
                        <svg class="logo" viewBox="0 0 661.77 599.03">
                            <path class="cls-2" style="fill: #172554;" d="m661.77,299.1c-17.12,8.76-33.88,18.13-50.25,28.1h-215.33v191.55c-19.77,25.3-38.04,51.83-54.67,79.46V0c16.63,27.62,34.9,54.16,54.67,79.46,45.44,58.21,98.82,109.95,158.47,153.58h-84.24c-26.29-22.4-51.09-46.5-74.23-72.12v113.12h220.39c14.76,8.84,29.83,17.19,45.2,25.06Z"/>
                            <path class="cls-1" style="fill: none; stroke: #000; stroke-miterlimit: 10; stroke-width: 2px;" d="m607.3,327.19h4.22c-.65.39-1.32.79-1.97,1.21-.76-.39-1.5-.8-2.25-1.21h0Z"/>
                            <path class="cls-3" style="fill: #fd6900;" d="m315.7,7.46v591.58c-16.67-27.03-34.94-61.29-54.67-86.06v-185.78h-88.08v91.71c-18-16.36-36.75-31.91-56.19-46.58v-146.46c19.44-14.67,38.19-30.22,56.19-46.58v94.75h88.08V85.23c13.94-17.49,27.14-35.58,39.56-54.23,5.19-7.75,10.24-15.6,15.11-23.54Z"/>
                            <path class="cls-3" style="fill: #fd6900;" d="m90.94,244.56v109.07c-13.27-9.22-26.85-18.04-40.68-26.44-16.37-9.96-33.14-19.33-50.25-28.1,15.37-7.87,30.43-16.22,45.2-25.06,15.6-9.31,30.86-19.14,45.74-29.48h0Z"/>
                        </svg>
                    </div>
                    
                    <div class="company-name">Funded Horizon</div>
                    <div class="certificate-title">Certificate</div>
                    <div class="certificate-subtitle">of achievement</div>
                </div>
                
                <div class="divider-container">
                    <div class="divider-line"></div>
                    <div class="divider-diamond"></div>
                    <div class="divider-line"></div>
                </div>
                
                <div class="main-section">
                    <div class="recipient-label">This Certificate is proudly awarded to</div>
                    <div class="recipient-name">Person Name</div>
                    
                    <div class="certificate-text">
                        for passing the Evolution stage at Funded Horizon.
                    </div>
                    
                    <div class="achievement-details">
                        <p><strong>Challenge Type:</strong> Phase 2 Evaluation</p>
                        <p><strong>Account Size:</strong> $100,000</p>
                        <p><strong>OrderID:</strong> FH-25F7G9-2025</p>
                    </div>
                    
                    <div class="motivation-text">
                        Your exceptional trading skills, discipline, and dedication have set you apart. This achievement is a testament to your <strong>commitment to excellence</strong> and marks the beginning of a promising journey.
                    </div>
                </div>
                
                <div class="footer-section">
                    <div class="signatures">
                        <div class="signature">
                            <div class="handwritten-signature">Maxwell Grant</div>
                            <div class="signature-line"></div>
                            <div class="signature-label">AUTHORIZED SIGNATURE</div>
                        </div>
                        
                        <div class="signature">
                            <div class="date-value">April 6, 2025</div>
                            <div class="signature-line"></div>
                            <div class="signature-label">DATE</div>
                        </div>
                    </div>
                    
                    <div class="certificate-id">CERTIFICATE ID: FH-25F7G9</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>