from sqlmodel import SQLModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum

class AffiliateCommissionType(str, Enum):
    FIXED_POINTS = "fixed_points"  # Fixed points per order
    PERCENTAGE = "percentage"      # Percentage of order value
    TIERED = "tiered"             # Different rates based on volume

class AffiliateStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"

class AffiliateSettings(SQLModel, table=True):
    """Global affiliate program settings"""
    __tablename__ = "affiliate_settings"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Commission settings
    commission_type: AffiliateCommissionType = Field(default=AffiliateCommissionType.FIXED_POINTS)
    fixed_points_per_order: int = Field(default=25)  # Points awarded per order
    percentage_rate: float = Field(default=5.0)      # Percentage of order value
    
    # Minimum requirements
    min_order_value: float = Field(default=0.0)      # Minimum order value to earn commission
    min_referrals_required: int = Field(default=1)   # Minimum referrals before earning commissions
    
    # Program status
    program_active: bool = Field(default=True)
    
    # Tier settings (for tiered commission)
    tier1_orders: int = Field(default=5)              # Orders needed for tier 1
    tier1_points: int = Field(default=25)             # Points per order in tier 1
    tier2_orders: int = Field(default=15)             # Orders needed for tier 2
    tier2_points: int = Field(default=35)             # Points per order in tier 2
    tier3_orders: int = Field(default=30)             # Orders needed for tier 3
    tier3_points: int = Field(default=50)             # Points per order in tier 3
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class AffiliateUser(SQLModel, table=True):
    """Individual affiliate user settings and stats"""
    __tablename__ = "affiliate_users"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(index=True, unique=True)  # One-to-one with User
    
    # Affiliate status
    status: AffiliateStatus = Field(default=AffiliateStatus.ACTIVE)
    joined_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Statistics
    total_referrals: int = Field(default=0)
    total_orders_from_referrals: int = Field(default=0)
    total_commission_earned: int = Field(default=0)  # Total points earned
    current_tier: int = Field(default=1)             # Current commission tier
    
    # Custom settings (override global settings if needed)
    custom_commission_rate: Optional[int] = Field(default=None)
    custom_percentage_rate: Optional[float] = Field(default=None)
    
    # Performance tracking
    last_commission_date: Optional[datetime] = Field(default=None)
    monthly_orders: int = Field(default=0)           # Orders this month
    monthly_commission: int = Field(default=0)       # Commission this month
    
    # Admin notes
    notes: Optional[str] = Field(default=None)

class AffiliateCommission(SQLModel, table=True):
    """Individual commission records"""
    __tablename__ = "affiliate_commissions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Relationship fields
    affiliate_user_id: int = Field(foreign_key="affiliate_users.id", index=True)
    referrer_user_id: int = Field(index=True)        # User who gets the commission
    referred_user_id: int = Field(index=True)        # User who placed the order
    order_id: int = Field(index=True)                # The order that generated commission
    
    # Commission details
    commission_points: int                           # Points awarded
    commission_type: AffiliateCommissionType        # Type of commission used
    order_value: float                              # Value of the order
    commission_rate: Optional[float] = Field(default=None)  # Rate used (if percentage)
    
    # Status and processing
    status: str = Field(default="pending")           # pending, paid, cancelled
    processed_at: Optional[datetime] = Field(default=None)
    
    # Metadata
    description: str                                 # Human readable description
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Additional tracking
    tier_level: Optional[int] = Field(default=None) # Tier level when commission was earned
    bonus_multiplier: float = Field(default=1.0)    # Any bonus multipliers applied

class AffiliatePayment(SQLModel, table=True):
    """Track affiliate payments/point distributions"""
    __tablename__ = "affiliate_payments"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    affiliate_user_id: int = Field(foreign_key="affiliate_users.id", index=True)
    
    # Payment details
    points_amount: int                               # Points paid out
    commission_ids: str                              # JSON array of commission IDs included
    
    # Payment info
    payment_method: str = Field(default="points")   # points, bank_transfer, etc.
    payment_reference: Optional[str] = Field(default=None)
    
    # Status
    status: str = Field(default="completed")         # pending, completed, failed
    processed_by: Optional[int] = Field(default=None)  # Admin user ID
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = Field(default=None)
    
    # Notes
    notes: Optional[str] = Field(default=None)

class AffiliateLink(SQLModel, table=True):
    """Track custom affiliate links and their performance"""
    __tablename__ = "affiliate_links"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(index=True)
    
    # Link details
    link_code: str = Field(unique=True, index=True)  # Custom link identifier
    destination_url: str                             # Where the link points
    
    # Tracking
    clicks: int = Field(default=0)
    conversions: int = Field(default=0)             # Successful registrations
    
    # Settings
    is_active: bool = Field(default=True)
    expires_at: Optional[datetime] = Field(default=None)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_clicked: Optional[datetime] = Field(default=None)
    
    # Description
    description: Optional[str] = Field(default=None)
