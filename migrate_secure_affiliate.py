#!/usr/bin/env python3
"""
Database migration script for Secure Affiliate System
Adds new columns and tables required for the secure affiliate system.
"""

import os
import sys
from sqlalchemy import create_engine, text, inspect
from sqlmodel import Session
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_database_url():
    """Get database URL from environment"""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not found")
        sys.exit(1)
    
    # Convert postgres:// to postgresql+psycopg2:// if needed
    if database_url.startswith("postgres://"):
        database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)
    
    return database_url

def check_column_exists(session, table_name, column_name):
    """Check if a column exists in a table"""
    try:
        result = session.exec(text(f"""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='{table_name}' AND column_name='{column_name}';
        """))
        return result.fetchone() is not None
    except Exception as e:
        print(f"Error checking column {column_name} in {table_name}: {e}")
        return False

def check_table_exists(session, table_name):
    """Check if a table exists"""
    try:
        result = session.exec(text(f"""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_name='{table_name}';
        """))
        return result.fetchone() is not None
    except Exception as e:
        print(f"Error checking table {table_name}: {e}")
        return False

def migrate_user_table(session):
    """Add new columns to user table"""
    print("🔄 Migrating user table...")
    
    # Add is_admin column
    if not check_column_exists(session, "user", "is_admin"):
        print("  ➕ Adding is_admin column...")
        session.exec(text("""
            ALTER TABLE "user" 
            ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
        """))
        print("  ✅ Added is_admin column")
    else:
        print("  ⏭️  is_admin column already exists")
    
    # Add referral_code_access_enabled column
    if not check_column_exists(session, "user", "referral_code_access_enabled"):
        print("  ➕ Adding referral_code_access_enabled column...")
        session.exec(text("""
            ALTER TABLE "user" 
            ADD COLUMN referral_code_access_enabled BOOLEAN DEFAULT FALSE;
        """))
        print("  ✅ Added referral_code_access_enabled column")
    else:
        print("  ⏭️  referral_code_access_enabled column already exists")
    
    # Make referral_code nullable (it might already be nullable)
    print("  🔄 Making referral_code column nullable...")
    try:
        session.exec(text("""
            ALTER TABLE "user" 
            ALTER COLUMN referral_code DROP NOT NULL;
        """))
        print("  ✅ Made referral_code nullable")
    except Exception as e:
        if "does not exist" in str(e).lower():
            print("  ⏭️  referral_code column constraint already nullable or doesn't exist")
        else:
            print(f"  ⚠️  Warning: Could not make referral_code nullable: {e}")

def create_referral_access_table(session):
    """Create referral_code_access table"""
    print("🔄 Creating referral_code_access table...")
    
    if not check_table_exists(session, "referral_code_access"):
        print("  ➕ Creating referral_code_access table...")
        session.exec(text("""
            CREATE TABLE referral_code_access (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL REFERENCES "user"(id),
                granted_by_admin_id INTEGER NOT NULL REFERENCES "user"(id),
                order_id INTEGER NOT NULL REFERENCES ordermodel(id),
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                is_revoked BOOLEAN DEFAULT FALSE,
                revoked_at TIMESTAMP,
                revoked_by_admin_id INTEGER REFERENCES "user"(id),
                revocation_reason TEXT
            );
        """))
        
        # Create indexes
        session.exec(text("""
            CREATE INDEX idx_referral_access_user_id ON referral_code_access(user_id);
            CREATE INDEX idx_referral_access_granted_by ON referral_code_access(granted_by_admin_id);
            CREATE INDEX idx_referral_access_order_id ON referral_code_access(order_id);
        """))
        
        print("  ✅ Created referral_code_access table with indexes")
    else:
        print("  ⏭️  referral_code_access table already exists")

def update_existing_users(session):
    """Update existing users based on migration strategy"""
    print("🔄 Updating existing users...")
    
    # Count users with referral codes
    result = session.exec(text("""
        SELECT COUNT(*) as count 
        FROM "user" 
        WHERE referral_code IS NOT NULL AND referral_code != '';
    """))
    users_with_codes = result.fetchone()[0]
    
    print(f"  📊 Found {users_with_codes} users with existing referral codes")
    
    if users_with_codes > 0:
        print("  🤔 Choose migration strategy for existing users:")
        print("     1. Grant access to all users with existing referral codes")
        print("     2. Disable access for all users (require admin approval)")
        print("     3. Skip this step (manual configuration required)")
        
        choice = input("  Enter choice (1/2/3): ").strip()
        
        if choice == "1":
            print("  ➕ Granting referral code access to all users with existing codes...")
            session.exec(text("""
                UPDATE "user" 
                SET referral_code_access_enabled = TRUE 
                WHERE referral_code IS NOT NULL AND referral_code != '';
            """))
            print(f"  ✅ Granted access to {users_with_codes} users")
            
        elif choice == "2":
            print("  🔒 Disabling referral code access for all users...")
            session.exec(text("""
                UPDATE "user" 
                SET referral_code_access_enabled = FALSE;
            """))
            print("  ✅ Disabled access for all users")
            
        else:
            print("  ⏭️  Skipping user updates - manual configuration required")

def create_admin_user(session):
    """Optionally create an admin user"""
    print("🔄 Admin user setup...")
    
    create_admin = input("  Do you want to create/designate an admin user? (y/n): ").strip().lower()
    
    if create_admin == 'y':
        admin_email = input("  Enter admin email: ").strip()
        
        if admin_email:
            # Check if user exists
            result = session.exec(text(f"""
                SELECT id, username FROM "user" WHERE email = '{admin_email}'
            """))

            user = result.fetchone()

            if user:
                print(f"  👤 Found user: {user[1]} (ID: {user[0]})")
                session.exec(text(f"""
                    UPDATE "user" SET is_admin = TRUE WHERE email = '{admin_email}'
                """))
                print(f"  ✅ Granted admin privileges to {admin_email}")
            else:
                print(f"  ❌ User with email {admin_email} not found")
                print("     Please create the user account first, then run this migration again")
    else:
        print("  ⏭️  Skipping admin user creation")
        print("     You can manually set is_admin = TRUE for admin users later")

def main():
    """Main migration function"""
    print("🚀 Starting Secure Affiliate System Migration")
    print("=" * 50)
    
    try:
        # Get database connection
        database_url = get_database_url()
        engine = create_engine(database_url, echo=False)
        
        with Session(engine) as session:
            print("✅ Connected to database")
            
            # Run migrations
            migrate_user_table(session)
            create_referral_access_table(session)
            
            # Commit structural changes
            session.commit()
            print("✅ Committed structural changes")
            
            # Update existing data
            update_existing_users(session)
            create_admin_user(session)
            
            # Final commit
            session.commit()
            print("✅ Migration completed successfully!")
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Migration Summary:")
    print("   ✅ Added is_admin column to user table")
    print("   ✅ Added referral_code_access_enabled column to user table")
    print("   ✅ Made referral_code column nullable")
    print("   ✅ Created referral_code_access tracking table")
    print("   ✅ Updated existing user data (if selected)")
    print("   ✅ Set up admin user (if selected)")
    print("\n🔧 Next Steps:")
    print("   1. Restart your application")
    print("   2. Test the new admin endpoints")
    print("   3. Review the SECURE_AFFILIATE_SYSTEM.md documentation")
    print("   4. Test user registration and order flow")

if __name__ == "__main__":
    main()
