# Referral System Documentation

## Overview
The referral system allows users to earn points by referring new users to the platform. When a new user registers using a referral code, the referring user receives 50 points as a bonus.

## Features

### 1. Unique Referral Codes
- Every user gets a unique 8-character referral code upon registration
- Referral codes are generated using uppercase letters and digits (e.g., "ABC12345")
- Codes are guaranteed to be unique across all users

### 2. Points System
- Users have a points balance that starts at 0
- Points can be earned through:
  - Referral bonuses (50 points per successful referral)
  - Task completion (variable points)
  - Admin adjustments
- All point transactions are tracked for audit purposes

### 3. Referral Tracking
- System tracks who referred whom
- Complete transaction history for all point movements
- Referral statistics showing total referrals and points earned

## API Endpoints

### User Registration with Referral
```
POST /auth/signup
{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "name": "New User",
    "phone_no": "+1234567890",
    "country": "USA",
    "address": "123 Main St",
    "referral_code": "ABC12345"  // Optional referral code
}
```

### Points Management

#### Get Points Balance
```
GET /points/balance
Authorization: Bearer <token>

Response:
{
    "user_id": 1,
    "username": "user1",
    "points_balance": 75,
    "referral_code": "XYZ98765"
}
```

#### Get Transaction History
```
GET /points/transactions?limit=50&offset=0
Authorization: Bearer <token>

Response:
[
    {
        "id": 1,
        "transaction_type": "referral_bonus",
        "points_amount": 50,
        "description": "Referral bonus for user newuser (<EMAIL>)",
        "reference_id": null,
        "created_at": "2025-01-15T10:30:00Z",
        "referred_user_id": 2
    }
]
```

#### Get Referral Statistics
```
GET /points/referrals
Authorization: Bearer <token>

Response:
{
    "referral_code": "XYZ98765",
    "total_referrals": 3,
    "total_referral_points_earned": 150,
    "referred_users": [
        {
            "id": 2,
            "username": "user2",
            "email": "<EMAIL>",
            "created_at": "2025-01-15T10:30:00Z"
        }
    ]
}
```

#### Award Task Completion Points
```
POST /points/task-completion?task_name=Complete Profile&points=25
Authorization: Bearer <token>

Response:
{
    "message": "Awarded 25 points for completing task: Complete Profile",
    "new_balance": 100,
    "transaction_id": 5
}
```

### Admin Endpoints

#### Add Points to User (Admin)
```
POST /points/admin/add
{
    "user_id": 1,
    "points": 100,
    "description": "Bonus points for participation",
    "reference_id": "bonus_2025_01"
}
```

#### Deduct Points from User (Admin)
```
POST /points/admin/deduct
{
    "user_id": 1,
    "points": 50,
    "description": "Penalty for violation",
    "reference_id": "penalty_001"
}
```

## Database Schema

### User Table Updates
- `referral_code`: Unique 8-character code for each user
- `referred_by`: ID of the user who referred this user (nullable)
- `points_balance`: Current points balance (default: 0)

### Points Transactions Table
- `id`: Primary key
- `user_id`: Foreign key to user table
- `transaction_type`: Type of transaction (referral_bonus, task_completion, etc.)
- `points_amount`: Points amount (positive for earning, negative for spending)
- `description`: Human-readable description
- `reference_id`: Optional reference to related entity
- `created_at`: Timestamp of transaction
- `referred_user_id`: ID of referred user (for referral transactions)

## Transaction Types
- `referral_bonus`: Points earned from referring new users
- `task_completion`: Points earned from completing tasks
- `purchase`: Points spent on purchases
- `admin_adjustment`: Manual adjustments by administrators
- `withdrawal`: Points withdrawn/redeemed

## Usage Examples

### 1. User Registration Flow
1. User A shares their referral code "ABC12345" with User B
2. User B registers with referral code "ABC12345"
3. System validates the referral code
4. User B is created with `referred_by` set to User A's ID
5. User A receives 50 points automatically
6. Transaction record is created for the referral bonus

### 2. Points Management
1. Users can check their points balance anytime
2. All transactions are logged with descriptions
3. Admins can add/deduct points as needed
4. Users earn points through various activities

## Testing
Run the test script to verify the referral system:
```bash
python test_referral_system.py
```

This will test:
- User registration without referral
- User registration with referral code
- Points balance checking
- Transaction history
- Referral statistics
- Task completion points

## Security Considerations
- Referral codes are generated using cryptographically secure random functions
- All point transactions are logged for audit trails
- Admin endpoints should be protected with proper authentication
- Referral codes are validated before awarding points

## Future Enhancements
- Referral tiers (different points for different user levels)
- Time-limited referral bonuses
- Referral contests and leaderboards
- Points expiration system
- Integration with reward redemption system
