"""
Debug script to test the giveaway system directly
This will help us identify why emails are not being sent
"""

from sqlmodel import Session, select
from db import engine
from models.user import User
from models.giveaway import Giveaway
from routes.giveaway import auto_enter_giveaway

def test_giveaway_entry():
    """Test giveaway entry directly"""
    print("🔍 Testing Giveaway Entry System")
    print("=" * 50)
    
    with Session(engine) as session:
        # Get a test user (you'll need to replace this with an actual user ID)
        user = session.exec(select(User).limit(1)).first()
        if not user:
            print("❌ No users found in database. Please create a user first.")
            return
        
        print(f"👤 Test User: {user.username} ({user.email})")
        
        # Get active giveaways
        giveaways = session.exec(
            select(Giveaway).where(Giveaway.status == "active")
        ).all()
        
        print(f"🎁 Found {len(giveaways)} giveaways:")
        for giveaway in giveaways:
            print(f"   - {giveaway.title}")
            print(f"     Min Account Size: ${giveaway.min_account_size:,.0f}")
            print(f"     Status: {giveaway.status}")
            print(f"     Start: {giveaway.start_date}")
            print(f"     End: {giveaway.end_date}")
        
        if not giveaways:
            print("❌ No active giveaways found!")
            return
        
        # Test with different account sizes
        test_cases = [
            ("$50,000", 50000),
            ("$75,000", 75000),
            ("$100,000", 100000),
            ("25000", 25000),  # Should not qualify
        ]
        
        for account_size_str, expected_value in test_cases:
            print(f"\n🧪 Testing with account size: {account_size_str}")
            print("-" * 30)
            
            # Simulate order creation
            fake_order_id = 999999
            
            try:
                auto_enter_giveaway(user, fake_order_id, account_size_str, session)
                print(f"✅ Giveaway entry test completed for {account_size_str}")
            except Exception as e:
                print(f"❌ Error during giveaway entry: {str(e)}")
                import traceback
                traceback.print_exc()

def check_giveaway_entries():
    """Check existing giveaway entries"""
    print("\n📊 Checking Existing Giveaway Entries")
    print("=" * 50)
    
    with Session(engine) as session:
        from models.giveaway import GiveawayEntry
        
        entries = session.exec(select(GiveawayEntry)).all()
        print(f"Found {len(entries)} giveaway entries:")
        
        for entry in entries:
            user = session.get(User, entry.user_id)
            giveaway = session.get(Giveaway, entry.giveaway_id)
            
            print(f"   - User: {user.username if user else 'Unknown'}")
            print(f"     Giveaway: {giveaway.title if giveaway else 'Unknown'}")
            print(f"     Account Size: ${entry.account_size:,.0f}")
            print(f"     Entry Date: {entry.entry_date}")
            print(f"     Status: {entry.status}")
            print()

def test_email_sending():
    """Test email sending directly"""
    print("\n📧 Testing Email Sending")
    print("=" * 50)
    
    try:
        from routes.giveaway import send_email
        
        # Test basic email sending
        test_email = "<EMAIL>"  # Replace with your email for testing
        subject = "🧪 Giveaway System Test Email"
        body = """
        <html>
        <body>
            <h2>Test Email</h2>
            <p>This is a test email from the giveaway system.</p>
            <p>If you receive this, the email system is working!</p>
        </body>
        </html>
        """
        
        print(f"Sending test email to: {test_email}")
        send_email(test_email, subject, body)
        print("✅ Test email sent successfully!")
        
    except Exception as e:
        print(f"❌ Error sending test email: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    print("🎁 Giveaway System Debug Test")
    print("=" * 50)
    
    # Test 1: Check giveaway entry system
    test_giveaway_entry()
    
    # Test 2: Check existing entries
    check_giveaway_entries()
    
    # Test 3: Test email sending (uncomment to test)
    # test_email_sending()
    
    print("\n" + "=" * 50)
    print("🔍 Debug Test Completed")
    print("\nIf you see debug messages above, the system is working.")
    print("If no debug messages appear, there might be an integration issue.")

if __name__ == "__main__":
    main()
