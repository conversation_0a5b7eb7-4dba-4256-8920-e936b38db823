"""
Test script for the enhanced points balance endpoint
This script tests the new points balance endpoint that includes referral information
"""

import requests
import json
from datetime import datetime

# Base URL for your API
BASE_URL = "http://localhost:8000"

def test_points_balance_endpoint():
    """Test the enhanced points balance endpoint"""
    print("💰 Testing Enhanced Points Balance Endpoint")
    print("=" * 50)
    
    # You need to login first to get a token
    # Replace these with actual user credentials
    login_data = {
        "username": "<EMAIL>",  # Replace with your email
        "password": "your_password"  # Replace with your password
    }
    
    print("1. Attempting to login...")
    login_response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print("Please update the credentials in this script with valid user credentials")
        return False
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Test the enhanced points balance endpoint
    print("\n2. Testing points balance endpoint...")
    response = requests.get(f"{BASE_URL}/points/balance", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        balance_data = response.json()
        print("✅ Points balance retrieved successfully!")
        print("\n📊 Points Balance Information:")
        print(f"   User ID: {balance_data['user_id']}")
        print(f"   Username: {balance_data['username']}")
        print(f"   Email: {balance_data['email']}")
        print(f"   Points Balance: {balance_data['points_balance']}")
        print(f"   Referral Code: {balance_data['referral_code']}")
        print(f"   Total Referrals: {balance_data['total_referrals']}")
        print(f"   Total Referral Points Earned: {balance_data['total_referral_points_earned']}")
        
        print(f"\n👥 Referred Users ({len(balance_data['referred_users'])}):")
        if balance_data['referred_users']:
            for i, user in enumerate(balance_data['referred_users'], 1):
                print(f"   {i}. {user['username']} ({user['email']})")
                print(f"      User ID: {user['id']}")
                print(f"      Joined: {user['created_at']}")
                print()
        else:
            print("   No users have registered with your referral code yet.")
        
        return balance_data
    else:
        print(f"❌ Failed to get points balance: {response.json()}")
        return None

def display_example_response():
    """Display an example of what the response looks like"""
    print("\n📋 Example Response Format:")
    print("=" * 50)
    
    example_response = {
        "user_id": 1,
        "username": "john_doe",
        "email": "<EMAIL>",
        "points_balance": 150,
        "referral_code": "ABC12345",
        "total_referrals": 3,
        "total_referral_points_earned": 150,
        "referred_users": [
            {
                "id": 2,
                "username": "jane_smith",
                "email": "<EMAIL>",
                "created_at": "2025-01-15T10:30:00Z"
            },
            {
                "id": 3,
                "username": "bob_wilson",
                "email": "<EMAIL>",
                "created_at": "2025-01-16T14:20:00Z"
            },
            {
                "id": 4,
                "username": "alice_brown",
                "email": "<EMAIL>",
                "created_at": "2025-01-17T09:15:00Z"
            }
        ]
    }
    
    print(json.dumps(example_response, indent=2))

def main():
    """Main test function"""
    print("💰 Enhanced Points Balance Endpoint Test")
    print("=" * 50)
    
    print("⚠️  IMPORTANT: Update the login credentials in this script before running!")
    print("   Edit the 'login_data' dictionary with valid user credentials")
    print()
    
    # Display example response format
    display_example_response()
    
    print("\n🔗 API Endpoint Information:")
    print("=" * 50)
    print("Endpoint: GET /points/balance")
    print("Authentication: Bearer token required")
    print("Response includes:")
    print("  • User's basic information (ID, username, email)")
    print("  • Current points balance")
    print("  • User's unique referral code")
    print("  • Total number of referrals")
    print("  • Total points earned from referrals")
    print("  • Detailed list of referred users with their info")
    
    print("\n📝 Usage Examples:")
    print("=" * 50)
    print("JavaScript:")
    print("""
fetch('/points/balance', {
    headers: {
        'Authorization': 'Bearer ' + token
    }
})
.then(response => response.json())
.then(data => {
    console.log('Points Balance:', data.points_balance);
    console.log('Referral Code:', data.referral_code);
    console.log('Total Referrals:', data.total_referrals);
    console.log('Referred Users:', data.referred_users);
});
    """)
    
    print("\nPython:")
    print("""
import requests

headers = {'Authorization': f'Bearer {token}'}
response = requests.get('http://localhost:8000/points/balance', headers=headers)
data = response.json()

print(f"Points: {data['points_balance']}")
print(f"Referrals: {data['total_referrals']}")
for user in data['referred_users']:
    print(f"  - {user['username']} ({user['email']})")
    """)
    
    # Uncomment the line below after updating credentials
    # test_points_balance_endpoint()

if __name__ == "__main__":
    main()
