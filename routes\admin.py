from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session, select, func
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

from db import get_session
from models.user import User
from models.order import OrderModel
from models.referral_access import ReferralCodeAccess
from auth import get_current_user
from utils.referral import enable_referral_code_access

admin_router = APIRouter(prefix="/admin", tags=["Admin"])

# Response models
class FirstOrderUserResponse(BaseModel):
    user_id: int
    username: str
    email: str
    name: str
    order_id: int
    order_created_at: datetime
    account_size: str
    challenge_type: str
    payment_method: str
    referral_code_access_enabled: bool
    has_referral_code: bool

class ReferralAccessGrantRequest(BaseModel):
    user_id: int
    order_id: int
    notes: Optional[str] = None

class ReferralAccessResponse(BaseModel):
    success: bool
    message: str
    user_id: int
    referral_code: Optional[str] = None

@admin_router.get("/first-order-users", response_model=List[FirstOrderUserResponse])
def get_first_order_users(
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user),
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    include_already_granted: bool = Query(False)
):
    """
    Get users who have created their first order and are eligible for referral code access.
    Any logged-in user can access this endpoint.
    """
    
    # Subquery to get the first order for each user
    first_order_subquery = (
        select(
            OrderModel.username,
            func.min(OrderModel.id).label("first_order_id")
        )
        .group_by(OrderModel.username)
        .subquery()
    )
    
    # Main query to get user and first order details
    query = (
        select(User, OrderModel)
        .join(OrderModel, User.username == OrderModel.username)
        .join(
            first_order_subquery,
            (OrderModel.username == first_order_subquery.c.username) &
            (OrderModel.id == first_order_subquery.c.first_order_id)
        )
    )
    
    # Filter based on whether to include users who already have access
    if not include_already_granted:
        query = query.where(User.referral_code_access_enabled == False)
    
    # Apply pagination
    query = query.offset(offset).limit(limit)
    
    results = session.exec(query).all()
    
    response_data = []
    for user, order in results:
        response_data.append(FirstOrderUserResponse(
            user_id=user.id,
            username=user.username,
            email=user.email,
            name=user.name,
            order_id=order.id,
            order_created_at=order.created_at,
            account_size=order.account_size,
            challenge_type=order.challenge_type,
            payment_method=order.payment_method,
            referral_code_access_enabled=user.referral_code_access_enabled,
            has_referral_code=user.referral_code is not None
        ))
    
    return response_data

@admin_router.post("/grant-referral-access", response_model=ReferralAccessResponse)
def grant_referral_code_access(
    request: ReferralAccessGrantRequest,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """
    Grant referral code access to a user who has completed their first order.
    Any logged-in user can grant access.
    """
    
    # Get the user
    user = session.get(User, request.user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Check if user already has access
    if user.referral_code_access_enabled:
        return ReferralAccessResponse(
            success=False,
            message="User already has referral code access",
            user_id=user.id,
            referral_code=user.referral_code
        )
    
    # Verify the order exists and belongs to the user
    order = session.get(OrderModel, request.order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )
    
    if order.username != user.username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Order does not belong to the specified user"
        )
    
    # Check if access was already granted for this user (in case of race condition)
    existing_access = session.exec(
        select(ReferralCodeAccess).where(ReferralCodeAccess.user_id == user.id)
    ).first()
    
    if existing_access and not existing_access.is_revoked:
        return ReferralAccessResponse(
            success=False,
            message="Referral code access already granted to this user",
            user_id=user.id,
            referral_code=user.referral_code
        )
    
    try:
        # Enable referral code access
        enable_referral_code_access(
            user=user,
            admin_user=current_user,
            order_id=order.id,
            session=session,
            notes=request.notes
        )
        
        session.commit()
        session.refresh(user)
        
        return ReferralAccessResponse(
            success=True,
            message=f"Referral code access granted successfully to {user.username}",
            user_id=user.id,
            referral_code=user.referral_code
        )
        
    except Exception as e:
        session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to grant referral code access: {str(e)}"
        )

@admin_router.get("/referral-access-history")
def get_referral_access_history(
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user),
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0)
):
    """
    Get history of referral code access grants.
    Any logged-in user can access this endpoint.
    """
    
    query = (
        select(ReferralCodeAccess, User, OrderModel)
        .join(User, ReferralCodeAccess.user_id == User.id)
        .join(OrderModel, ReferralCodeAccess.order_id == OrderModel.id)
        .order_by(ReferralCodeAccess.granted_at.desc())
        .offset(offset)
        .limit(limit)
    )
    
    results = session.exec(query).all()
    
    history = []
    for access, user, order in results:
        # Get admin who granted access
        admin = session.get(User, access.granted_by_admin_id)
        
        history.append({
            "access_id": access.id,
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "order_id": order.id,
            "account_size": order.account_size,
            "granted_at": access.granted_at,
            "granted_by_admin": admin.username if admin else "Unknown",
            "notes": access.notes,
            "is_revoked": access.is_revoked,
            "revoked_at": access.revoked_at
        })
    
    return {
        "history": history,
        "total_count": len(history),
        "offset": offset,
        "limit": limit
    }
