#!/usr/bin/env python3
"""
Test script for the updated create order endpoint without image upload.
"""

import requests
import json
import sys
import random

BASE_URL = "http://localhost:8000"  # Adjust if your app runs on different port

def test_user_login():
    """Test user login and get token"""
    print("🧪 Testing user login...")
    
    # Try to login with existing user
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data["access_token"]
            print("   ✅ Login successful")
            return token
        else:
            print(f"   ❌ Login failed: {response.json()}")
            return None
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return None

def test_create_order_without_image(token):
    """Test creating an order without image upload"""
    print("🧪 Testing create order without image...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Generate unique transaction ID
    txid = f"test_tx_{random.randint(100000, 999999)}"
    
    # Order data (form data)
    order_data = {
        "challenge_type": "Phase 1",
        "account_size": "$10,000",
        "platform": "MT4",
        "payment_method": "Credit Card",
        "txid": txid
    }
    
    try:
        response = requests.post(f"{BASE_URL}/order/order", data=order_data, headers=headers)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            order_response = response.json()
            print("   ✅ SUCCESS - Order created without image")
            
            # Display order details
            print(f"   📋 Order ID: {order_response.get('id', 'N/A')}")
            print(f"   👤 Username: {order_response.get('username', 'N/A')}")
            print(f"   💰 Account Size: {order_response.get('account_size', 'N/A')}")
            print(f"   🎯 Challenge Type: {order_response.get('challenge_type', 'N/A')}")
            print(f"   🖥️  Platform: {order_response.get('platform', 'N/A')}")
            print(f"   💳 Payment Method: {order_response.get('payment_method', 'N/A')}")
            print(f"   🔗 Transaction ID: {order_response.get('txid', 'N/A')}")
            
            # Check if images field exists (should be empty or not present)
            images = order_response.get('images', [])
            print(f"   🖼️  Images: {len(images)} (should be 0)")
            
            if len(images) == 0:
                print("   ✅ PASS: No images attached to order")
            else:
                print("   ⚠️  WARNING: Images found when none should exist")
            
            return order_response
        else:
            print(f"   ❌ FAILED - {response.json()}")
            return None
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return None

def test_create_order_duplicate_txid(token):
    """Test creating an order with duplicate transaction ID"""
    print("🧪 Testing duplicate transaction ID handling...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Use the same transaction ID twice
    txid = f"duplicate_tx_{random.randint(100000, 999999)}"
    
    order_data = {
        "challenge_type": "Phase 2",
        "account_size": "$25,000",
        "platform": "MT5",
        "payment_method": "Bank Transfer",
        "txid": txid
    }
    
    try:
        # First order should succeed
        response1 = requests.post(f"{BASE_URL}/order/order", data=order_data, headers=headers)
        print(f"   First order status: {response1.status_code}")
        
        if response1.status_code == 200:
            print("   ✅ First order created successfully")
            
            # Second order with same txid should fail
            response2 = requests.post(f"{BASE_URL}/order/order", data=order_data, headers=headers)
            print(f"   Second order status: {response2.status_code}")
            
            if response2.status_code == 400:
                error_detail = response2.json().get("detail", "")
                if "Transaction ID already used" in error_detail:
                    print("   ✅ PASS: Duplicate transaction ID correctly rejected")
                    return True
                else:
                    print(f"   ❌ FAIL: Wrong error message - {error_detail}")
                    return False
            else:
                print("   ❌ FAIL: Duplicate transaction ID was accepted")
                return False
        else:
            print(f"   ❌ First order failed: {response1.json()}")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Create Order Endpoint (No Image Upload)")
    print("=" * 60)
    
    # Test 1: Login
    token = test_user_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        sys.exit(1)
    
    # Test 2: Create order without image
    order = test_create_order_without_image(token)
    if not order:
        print("❌ Create order test failed")
        sys.exit(1)
    
    # Test 3: Test duplicate transaction ID handling
    duplicate_test = test_create_order_duplicate_txid(token)
    
    print("\n" + "=" * 60)
    print("🎉 Create Order Tests Completed!")
    print("\n📋 Summary:")
    print("   ✅ Image upload functionality removed")
    print("   ✅ Order creation works without image parameter")
    print("   ✅ All required fields still validated")
    print("   ✅ Duplicate transaction ID prevention works")
    print("   ✅ Order response format maintained")
    
    print("\n🔧 Updated Endpoint:")
    print("   POST /order/order")
    print("   Parameters:")
    print("     - challenge_type (required)")
    print("     - account_size (required)")
    print("     - platform (required)")
    print("     - payment_method (required)")
    print("     - txid (required)")
    print("     - Authorization header (required)")
    print("   Removed:")
    print("     - image parameter (no longer accepted)")

if __name__ == "__main__":
    main()
