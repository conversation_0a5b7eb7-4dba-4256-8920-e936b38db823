"""
<PERSON><PERSON><PERSON> to create default affiliate program settings
Run this script to initialize the affiliate program in your database
"""

from sqlmodel import Session
from db import engine
from models.affiliate import AffiliateSettings, AffiliateCommissionType

def create_default_affiliate_settings():
    """Create default affiliate program settings"""
    
    with Session(engine) as session:
        # Check if settings already exist
        from sqlmodel import select
        existing_settings = session.exec(select(AffiliateSettings)).first()
        if existing_settings:
            print(f"Affiliate settings already exist:")
            print(f"   Commission Type: {existing_settings.commission_type}")
            print(f"   Fixed Points per Order: {existing_settings.fixed_points_per_order}")
            print(f"   Program Active: {existing_settings.program_active}")
            return existing_settings
        
        # Create new affiliate settings
        settings = AffiliateSettings(
            commission_type=AffiliateCommissionType.TIERED,  # Use tiered system
            fixed_points_per_order=25,                       # Base points per order
            percentage_rate=5.0,                            # 5% if using percentage
            min_order_value=0.0,                            # No minimum order value
            min_referrals_required=1,                       # Need at least 1 referral
            program_active=True,                            # Program is active
            
            # Tier settings
            tier1_orders=0,                                 # Bronze: 0+ orders
            tier1_points=25,                                # Bronze: 25 points per order
            tier2_orders=10,                                # Silver: 10+ orders from referrals
            tier2_points=35,                                # Silver: 35 points per order
            tier3_orders=25,                                # Gold: 25+ orders from referrals
            tier3_points=50                                 # Gold: 50 points per order
        )
        
        session.add(settings)
        session.commit()
        session.refresh(settings)
        
        print(f"✅ Created default affiliate settings:")
        print(f"   Commission Type: {settings.commission_type}")
        print(f"   Program Active: {settings.program_active}")
        print(f"   Tier System:")
        print(f"     🥉 Bronze (0+ orders): {settings.tier1_points} points per order")
        print(f"     🥈 Silver ({settings.tier2_orders}+ orders): {settings.tier2_points} points per order")
        print(f"     🥇 Gold ({settings.tier3_orders}+ orders): {settings.tier3_points} points per order")
        
        return settings

def main():
    """Main function to run the script"""
    print("💰 Creating Default Affiliate Settings...")
    print("=" * 50)
    
    try:
        settings = create_default_affiliate_settings()
        print("\n✅ Affiliate program initialized successfully!")
        
        print("\n📋 How the Affiliate Program Works:")
        print("=" * 50)
        print("1. Users automatically become affiliates when they register")
        print("2. When a referred user places ANY order, the referrer earns points")
        print("3. Commission is based on tier level:")
        print("   • Bronze (0+ orders from referrals): 25 points per order")
        print("   • Silver (10+ orders from referrals): 35 points per order") 
        print("   • Gold (25+ orders from referrals): 50 points per order")
        print("4. Points are automatically added to the referrer's balance")
        print("5. Email notifications are sent for each commission earned")
        
        print("\n🔗 API Endpoints:")
        print("=" * 50)
        print("• GET /affiliate/stats - View affiliate statistics")
        print("• GET /affiliate/commissions - View commission history")
        print("• GET /affiliate/settings - View program settings")
        print("• POST /affiliate/admin/settings - Update settings (admin)")
        print("• GET /affiliate/admin/users - View all affiliates (admin)")
        
        print("\n📧 Email Templates:")
        print("=" * 50)
        print("• Commission Earned: /email-preview/affiliate-commission")
        print("• Tier Upgrade: /email-preview/affiliate-tier-upgrade")
        
        print("\n🧪 Testing:")
        print("=" * 50)
        print("1. Create a user account")
        print("2. Register another user with the first user's referral code")
        print("3. Have the referred user place an order")
        print("4. Check that the referrer receives commission points")
        print("5. Verify commission email is sent")
        
    except Exception as e:
        print(f"❌ Error creating affiliate settings: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    main()
