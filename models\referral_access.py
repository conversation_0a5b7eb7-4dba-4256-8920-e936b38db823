from sqlmodel import SQLModel, <PERSON>
from datetime import datetime
from typing import Optional

class ReferralCodeAccess(SQLModel, table=True):
    """Track when and by which admin referral code access was granted to users"""
    __tablename__ = "referral_code_access"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id", index=True)
    granted_by_admin_id: int = Field(foreign_key="user.id", index=True)
    order_id: int = Field(foreign_key="ordermodel.id", index=True)  # The first order that qualified them
    granted_at: datetime = Field(default_factory=datetime.utcnow)
    notes: Optional[str] = Field(default=None)  # Admin notes about why access was granted
    
    # Track if access was ever revoked
    is_revoked: bool = Field(default=False)
    revoked_at: Optional[datetime] = Field(default=None)
    revoked_by_admin_id: Optional[int] = Field(default=None, foreign_key="user.id")
    revocation_reason: Optional[str] = Field(default=None)
